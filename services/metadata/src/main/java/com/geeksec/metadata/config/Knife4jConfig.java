package com.geeksec.metadata.config;

import com.geeksec.services.common.infrastructure.config.BaseKnife4jConfig;
import io.swagger.v3.oas.models.OpenAPI;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 元数据服务 Knife4j API 文档配置类
 *
 * 继承通用配置基类，提供元数据服务特定的 API 文档信息
 * 使用 Knife4j 4.5.0 和 OpenAPI 3.0 规范
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Configuration
public class Knife4jConfig extends BaseKnife4jConfig {

    /**
     * 配置 OpenAPI 文档
     *
     * @return OpenAPI 实例
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return createOpenAPI();
    }

    @Override
    protected String getApiTitle() {
        return "NTA 3.0 元数据服务 API";
    }

    @Override
    protected String getApiDescription() {
        return "提供标签、协议知识库、攻击阶段、系统字典等元数据的管理和查询功能。" +
               "兼容 NTA 2.0 的数据字典接口，支持系统元数据的统一管理。";
    }
}
