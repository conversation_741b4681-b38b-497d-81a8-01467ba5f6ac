package com.geeksec.rule.domain.enums;

import lombok.Getter;

/**
 * 检测器类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum DetectorType {
    
    /**
     * 证书检测器
     */
    CERTIFICATE("证书检测", "检测SSL/TLS证书相关的安全威胁"),
    
    /**
     * 网络检测器
     */
    NETWORK("网络检测", "检测网络流量中的异常行为"),
    
    /**
     * DNS检测器
     */
    DNS("DNS检测", "检测DNS查询和响应中的异常"),
    
    /**
     * HTTP检测器
     */
    HTTP("HTTP检测", "检测HTTP协议中的安全威胁"),
    
    /**
     * SSL检测器
     */
    SSL("SSL检测", "检测SSL/TLS协议中的安全问题"),
    
    /**
     * 指纹检测器
     */
    FINGERPRINT("指纹检测", "基于指纹识别的威胁检测"),
    
    /**
     * C2检测器
     */
    C2("C2检测", "检测命令与控制服务器通信"),
    
    /**
     * APT检测器
     */
    APT("APT检测", "检测高级持续性威胁攻击"),
    
    /**
     * 恶意软件检测器
     */
    MALWARE("恶意软件检测", "检测恶意软件相关活动"),
    
    /**
     * 暴力破解检测器
     */
    BRUTEFORCE("暴力破解检测", "检测暴力破解攻击"),
    
    /**
     * 异常检测器
     */
    ANOMALY("异常检测", "基于机器学习的异常行为检测");

    private final String displayName;
    private final String description;

    DetectorType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }



    /**
     * 根据显示名称获取检测器类型
     */
    public static DetectorType fromDisplayName(String displayName) {
        for (DetectorType type : values()) {
            if (type.displayName.equals(displayName)) {
                return type;
            }
        }
        return NETWORK;
    }
}
