package com.geeksec.rule.application.service.impl;

import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.services.common.shared.dto.PageResultVo;
import com.geeksec.rule.application.service.FilterRuleService;
import com.geeksec.rule.domain.entity.FilterRule;
import com.geeksec.rule.infrastructure.mapper.FilterRuleMapper;
import com.geeksec.rule.interfaces.dto.FilterRuleCreateDTO;
import com.geeksec.rule.interfaces.dto.FilterRuleQueryDTO;
import com.geeksec.rule.interfaces.vo.FilterRuleVO;
import com.geeksec.rule.interfaces.vo.FilterModeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 过滤规则服务实现类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FilterRuleServiceImpl implements FilterRuleService {

    private final FilterRuleMapper filterRuleMapper;
    private final RuleValidationService ruleValidationService;

    @Override
    @Transactional
    public ApiResponse<Void> createFilterRule(FilterRuleCreateDTO createDTO) {
        log.info("创建过滤规则: {}", createDTO);
        
        try {
            // 验证规则
            var validationResult = ruleValidationService.validateFilterRule(createDTO);
            if (!validationResult.isValid()) {
                return ApiResponse.badRequest("规则验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            // 创建规则实体
            FilterRule rule = new FilterRule();
            rule.setTaskId(createDTO.getTaskId());
            rule.setType(createDTO.getType());
            rule.setFilterJson(createDTO.getFilterInfo().toString());
            rule.setRuleHash(validationResult.getRuleHash());
            rule.setCreatedAt(LocalDateTime.now());
            rule.setUpdatedAt(LocalDateTime.now());

            // 保存规则
            filterRuleMapper.insert(rule);
            
            log.info("过滤规则创建成功，规则ID: {}", rule.getId());
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("创建过滤规则失败", e);
            return ApiResponse.error("创建过滤规则失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> updateFilterRule(Long id, FilterRuleCreateDTO updateDTO) {
        log.info("更新过滤规则，ID: {}, 数据: {}", id, updateDTO);
        
        try {
            // 检查规则是否存在
            FilterRule existingRule = filterRuleMapper.selectById(id);
            if (existingRule == null) {
                return ApiResponse.error(404, "过滤规则不存在");
            }

            // 验证规则
            var validationResult = ruleValidationService.validateFilterRule(updateDTO);
            if (!validationResult.isValid()) {
                return ApiResponse.badRequest("规则验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            // 更新规则
            existingRule.setType(updateDTO.getType());
            existingRule.setFilterJson(updateDTO.getFilterInfo().toString());
            existingRule.setRuleHash(validationResult.getRuleHash());
            existingRule.setUpdatedAt(LocalDateTime.now());

            filterRuleMapper.updateById(existingRule);
            
            log.info("过滤规则更新成功，ID: {}", id);
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("更新过滤规则失败，ID: {}", id, e);
            return ApiResponse.error("更新过滤规则失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> deleteFilterRules(List<Long> ids) {
        log.info("删除过滤规则，IDs: {}", ids);
        
        try {
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.badRequest("规则ID列表不能为空");
            }

            // 批量删除
            filterRuleMapper.deleteBatchIds(ids);
            
            log.info("过滤规则删除成功，删除数量: {}", ids.size());
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("删除过滤规则失败，IDs: {}", ids, e);
            return ApiResponse.error("删除过滤规则失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> deleteFilterRulesByTaskId(Integer taskId) {
        log.info("根据任务ID删除过滤规则，任务ID: {}", taskId);
        
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID无效");
            }

            // 根据任务ID删除规则
            filterRuleMapper.delete(
                filterRuleMapper.query()
                    .where(FilterRule::getTaskId).eq(taskId)
                    .build()
            );
            
            log.info("根据任务ID删除过滤规则成功，任务ID: {}", taskId);
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("根据任务ID删除过滤规则失败，任务ID: {}", taskId, e);
            return ApiResponse.error("删除过滤规则失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<PageResultVo<FilterRuleVO>> getFilterRules(FilterRuleQueryDTO queryDTO) {
        log.info("分页查询过滤规则，查询条件: {}", queryDTO);
        
        try {
            // TODO: 实现分页查询逻辑
            // 这里需要根据实际的数据访问层实现
            
            // 临时返回空结果
            PageResultVo<FilterRuleVO> pageResult = new PageResultVo<>();
            pageResult.setRecords(List.of());
            pageResult.setTotal(0);
            pageResult.setCurrentPage(queryDTO.getCurrentPage());
            pageResult.setPageSize(queryDTO.getPageSize());
            
            return ApiResponse.success(pageResult);
            
        } catch (Exception e) {
            log.error("分页查询过滤规则失败", e);
            return ApiResponse.error("查询过滤规则失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> modifyFilterMode(Integer taskId, Integer mode) {
        log.info("修改过滤模式，任务ID: {}, 模式: {}", taskId, mode);
        
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID无效");
            }

            if (mode == null || (mode != 0 && mode != 1)) {
                return ApiResponse.badRequest("过滤模式无效，只能是 0（丢弃）或 1（保留）");
            }

            // TODO: 实现过滤模式修改逻辑
            // 这里需要根据实际的业务逻辑实现
            
            log.info("过滤模式修改成功，任务ID: {}, 新模式: {}", taskId, mode);
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("修改过滤模式失败，任务ID: {}, 模式: {}", taskId, mode, e);
            return ApiResponse.error("修改过滤模式失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<FilterModeVO> getFilterMode(Integer taskId) {
        log.info("获取过滤模式，任务ID: {}", taskId);
        
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID无效");
            }

            // TODO: 实现获取过滤模式逻辑
            FilterModeVO modeVO = new FilterModeVO();
            modeVO.setTaskId(taskId);
            modeVO.setMode(1); // 默认保留模式
            
            return ApiResponse.success(modeVO);
            
        } catch (Exception e) {
            log.error("获取过滤模式失败，任务ID: {}", taskId, e);
            return ApiResponse.error("获取过滤模式失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Object> importFilterRulesFromCsv(Integer taskId, String csvContent) {
        log.info("批量导入过滤规则，任务ID: {}", taskId);
        
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID无效");
            }

            if (csvContent == null || csvContent.trim().isEmpty()) {
                return ApiResponse.badRequest("CSV内容不能为空");
            }

            // TODO: 实现CSV导入逻辑
            return ApiResponse.success("导入功能待实现");
            
        } catch (Exception e) {
            log.error("批量导入过滤规则失败，任务ID: {}", taskId, e);
            return ApiResponse.error("导入过滤规则失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<String> exportFilterRuleTemplate() {
        log.info("导出过滤规则模板");
        
        try {
            // TODO: 实现模板导出逻辑
            return ApiResponse.success("模板导出功能待实现");
            
        } catch (Exception e) {
            log.error("导出过滤规则模板失败", e);
            return ApiResponse.error("导出模板失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<List<List<String>>> getFilterRulesForExport(Integer taskId) {
        log.info("获取过滤规则列表用于导出，任务ID: {}", taskId);
        
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID无效");
            }

            // TODO: 实现导出数据获取逻辑
            return ApiResponse.success(List.of());
            
        } catch (Exception e) {
            log.error("获取过滤规则导出数据失败，任务ID: {}", taskId, e);
            return ApiResponse.error("获取导出数据失败: " + e.getMessage());
        }
    }

    /**
     * 将实体转换为VO
     */
    private FilterRuleVO convertToVO(FilterRule rule) {
        FilterRuleVO vo = new FilterRuleVO();
        vo.setId(rule.getId());
        vo.setTaskId(rule.getTaskId());
        vo.setType(rule.getType());
        // 转换时间格式：LocalDateTime -> Integer (时间戳)
        if (rule.getCreatedAt() != null) {
            vo.setCreatedTime((int) rule.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toEpochSecond());
        }
        if (rule.getUpdatedAt() != null) {
            vo.setUpdatedTime((int) rule.getUpdatedAt().atZone(java.time.ZoneId.systemDefault()).toEpochSecond());
        }
        // TODO: 解析 filterJson 并设置具体的过滤信息
        return vo;
    }
}
