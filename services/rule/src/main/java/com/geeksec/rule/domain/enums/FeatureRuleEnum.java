package com.geeksec.rule.domain.enums;

import lombok.Getter;

@Getter
public enum FeatureRuleEnum {

    BASE("base_rule","基础对象"),
    DETAIL("detail_respond","复杂规则"),
    IP("ip_rules","ip规则"),
    PRO("pro_rules","协议规则"),
    KEY("key_rules","特征规则"),
    REGEX("regex_rules","正则表达规则"),
    DOMAIN("domain_rules","域名规则");

    private String type;

    private String msg;

    FeatureRuleEnum(String type, String msg) {
        this.type = type;
        this.msg = msg;
    }



    public static FeatureRuleEnum checkType(String type){
        for (FeatureRuleEnum ok : FeatureRuleEnum.values()) {
            if(ok.type.equals(type)){
                return ok;
            }
        }
        return null;
    }
}
