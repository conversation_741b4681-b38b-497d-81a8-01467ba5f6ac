package com.geeksec.rule.domain.enums;

import lombok.Getter;

/**
 * 检测规则类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum DetectionRuleEnum {

    BASE("base_rule", "基础对象"),
    DETAIL("detail_respond", "复杂规则"),
    IP("ip_rules", "IP规则"),
    PROTOCOL("pro_rules", "协议规则"),
    KEYWORD("keyword_rules", "特征字规则"),
    REGEX("regex_rules", "正则表达式规则"),
    DOMAIN("domain_rules", "域名规则");

    private final String type;
    private final String description;

    DetectionRuleEnum(String type, String description) {
        this.type = type;
        this.description = description;
    }

    /**
     * 根据类型查找枚举
     * 
     * @param type 规则类型
     * @return 对应的枚举，如果未找到返回null
     */
    public static DetectionRuleEnum findByType(String type) {
        if (type == null) {
            return null;
        }
        
        for (DetectionRuleEnum ruleEnum : values()) {
            if (ruleEnum.type.equals(type)) {
                return ruleEnum;
            }
        }
        return null;
    }

    /**
     * 检查类型是否有效
     * 
     * @param type 规则类型
     * @return 是否有效
     */
    public static boolean isValidType(String type) {
        return findByType(type) != null;
    }
}
