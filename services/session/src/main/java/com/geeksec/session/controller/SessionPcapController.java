package com.geeksec.session.controller;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.session.service.SessionService;
import com.geeksec.session.model.dto.pcap.SessionPcapInfo;
import com.geeksec.session.model.dto.pcap.PcapDownloadRequest;
import com.geeksec.session.model.dto.pcap.PcapDownloadResponse;
import com.geeksec.session.model.dto.pcap.PcapDownloadStatus;
import com.geeksec.session.model.dto.pcap.PcapFileStatus;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 会话PCAP文件控制器
 * 专门负责PCAP文件相关操作：文件信息查询、下载、状态检查等
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/session/pcap")
@RequiredArgsConstructor
@Validated
@Tag(name = "会话PCAP管理", description = "专门处理PCAP文件相关操作：查询、下载、状态检查")
public class SessionPcapController {

    private final SessionService sessionService;

    /**
     * 单次操作最大会话数量限制
     */
    private static final int MAX_SESSION_COUNT = 1000;

    /**
     * 根据会话ID列表查询PCAP文件信息
     * 供其他模块调用，获取PCAP文件元数据
     */
    @PostMapping("/files")
    @Operation(summary = "查询PCAP文件信息", description = "根据会话ID列表查询对应的PCAP文件信息，供其他模块使用")
    public ApiResponse<List<SessionPcapInfo>> getSessionPcapFiles(
            @Parameter(description = "会话ID列表", required = true) @RequestBody List<String> sessionIds) {
        try {
            if (sessionIds == null || sessionIds.isEmpty()) {
                return ApiResponse.badRequest("会话ID列表不能为空");
            }

            if (sessionIds.size() > MAX_SESSION_COUNT) {
                return ApiResponse.badRequest("单次查询会话数量不能超过" + MAX_SESSION_COUNT + "个");
            }

            List<SessionPcapInfo> pcapFiles = sessionService.getSessionPcapFiles(sessionIds);
            return ApiResponse.success(pcapFiles);
        } catch (Exception e) {
            log.error("查询PCAP文件信息失败", e);
            return ApiResponse.error("查询PCAP文件信息失败：" + e.getMessage());
        }
    }

    /**
     * 统一的PCAP下载接口，支持传入单个或多个会话ID
     */
    @PostMapping("/download")
    @Operation(summary = "下载PCAP文件", description = "根据会话ID列表下载对应的PCAP文件。支持单个会话（传入1个ID）或批量会话（传入多个ID），多个文件会自动合并为单个文件")
    public ApiResponse<PcapDownloadResponse> downloadSessionsPcap(
            @Parameter(description = "PCAP下载请求，sessionIds支持单个或多个会话ID", required = true) @RequestBody PcapDownloadRequest request) {
        try {
            if (request.sessionIds() == null || request.sessionIds().isEmpty()) {
                return ApiResponse.badRequest("会话ID列表不能为空");
            }

            if (request.sessionIds().size() > MAX_SESSION_COUNT) {
                return ApiResponse.badRequest("单次下载会话数量不能超过" + MAX_SESSION_COUNT + "个");
            }

            PcapDownloadResponse response = sessionService.downloadSessionsPcap(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("下载PCAP文件失败", e);
            return ApiResponse.error("下载失败：" + e.getMessage());
        }
    }

    /**
     * 查询PCAP下载任务状态
     */
    @GetMapping("/download/{taskId}/status")
    @Operation(summary = "查询下载任务状态", description = "查询PCAP下载任务的处理状态")
    public ApiResponse<PcapDownloadStatus> getDownloadTaskStatus(
            @Parameter(description = "任务ID", required = true) @PathVariable String taskId) {
        try {
            PcapDownloadStatus status = sessionService.getDownloadTaskStatus(taskId);
            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("查询下载任务状态失败: taskId={}", taskId, e);
            return ApiResponse.error("查询任务状态失败：" + e.getMessage());
        }
    }

    /**
     * 检查PCAP文件是否存在
     */
    @PostMapping("/check")
    @Operation(summary = "检查PCAP文件", description = "检查指定会话的PCAP文件是否存在")
    public ApiResponse<List<PcapFileStatus>> checkPcapFiles(
            @Parameter(description = "会话ID列表", required = true) @RequestBody List<String> sessionIds) {
        try {
            List<PcapFileStatus> statuses = sessionService.checkPcapFiles(sessionIds);
            return ApiResponse.success(statuses);
        } catch (Exception e) {
            log.error("检查PCAP文件失败", e);
            return ApiResponse.error("检查文件失败：" + e.getMessage());
        }
    }

}
