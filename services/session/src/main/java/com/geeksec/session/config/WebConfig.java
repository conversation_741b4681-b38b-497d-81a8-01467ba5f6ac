package com.geeksec.session.config;

import com.geeksec.services.common.infrastructure.config.WebMvcBaseConfig;
import com.geeksec.services.common.infrastructure.interceptor.RequestLogInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

/**
 * Session 服务 Web 配置类
 * 继承 common 模块的基础配置，添加服务特定的拦截器
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig extends WebMvcBaseConfig {

    private final RequestLogInterceptor requestLogInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 先调用父类的拦截器配置
        super.addInterceptors(registry);

        // 添加请求日志拦截器
        registry.addInterceptor(requestLogInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns(
                        "/api/health",
                        "/api/actuator/**",
                        "/swagger-ui/**",
                        "/v3/api-docs/**",
                        "/doc.html",
                        "/webjars/**"
                );
    }
}
