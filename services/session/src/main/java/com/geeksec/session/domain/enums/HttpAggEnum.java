package com.geeksec.session.domain.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum HttpAggEnum {
    //支持的聚合字段：客户端IP、服务端IP、服务端端口、网址（URL）、主站、源端HTTP指纹（HTTP-UA）
    //客户端=s、源   服务端=d、目标
    SIP("sIp","sIp"),
    DIP("dIp","dIp"),
    DPort("dPort","dPort"),
    Url("Url","Url"),
    Host("Host","Host.keyword"),
    //http 指纹
    USER_AGENT("Client.User-Agent","Client.User-Agent");

    private final String field;
    //方便es查询字段
    private final String esUse;

    HttpAggEnum(String field, String esUse){
        this.field = field;
        this.esUse = esUse;
    }



    public static Boolean checkField(String field){
        for(HttpAggEnum aggEnum : HttpAggEnum.values()){
            if(aggEnum.getField().equals(field)){
                return true;
            }
        }
        return false;
    }

    public static String getEsField(String field){
        for(HttpAggEnum aggEnum : HttpAggEnum.values()){
            if(aggEnum.getField().equals(field)){
                return aggEnum.getEsUse();
            }
        }
        return null;
    }

    /**
     * 获取前端对应字段集合
     * @return
     */
    public static List<String> getAllField(){
        List<String> list = new ArrayList<>();
        for(HttpAggEnum aggEnum : HttpAggEnum.values()){
            list.add(aggEnum.getField());
        }
        return list;
    }
}
