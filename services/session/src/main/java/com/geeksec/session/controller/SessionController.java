package com.geeksec.session.controller;

import com.geeksec.services.common.controller.BaseController;
import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.session.model.dto.session.SessionListResponse;
import com.geeksec.session.model.dto.session.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;
import com.geeksec.session.service.SessionService;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话管理控制器 - 基于Doris只读查询
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Tag(name = "会话管理", description = "提供会话查询、统计、分析等操作")
@RestController
@RequestMapping("/api/sessions")
@RequiredArgsConstructor
public class SessionController extends BaseController {

    private final SessionService sessionService;

    @Operation(summary = "获取会话详情", description = "根据ID获取会话详情")
    @GetMapping("/{id}")
    public ApiResponse<Session> getSessionById(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String id) {
        try {
            log.debug("获取会话详情, ID: {}", id);
            Session result = sessionService.getSessionById(id);
            return success(result);
        } catch (Exception e) {
            log.error("获取会话详情失败, ID: {}", id, e);
            return ApiResponse.error("获取会话详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "分页查询会话列表", description = "根据条件分页查询会话列表")
    @PostMapping("/list")
    public ApiResponse<Page<SessionListResponse>> querySessionList(
            @Parameter(description = "查询条件", required = true)
            @Valid @RequestBody SessionQueryRequest queryRequest) {
        try {
            log.debug("分页查询会话列表, 参数: {}", queryRequest);
            Page<SessionListResponse> result = sessionService.querySessionList(queryRequest);
            return success(result);
        } catch (Exception e) {
            log.error("分页查询会话列表失败", e);
            return ApiResponse.error("分页查询会话列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "统计会话数量", description = "根据条件统计会话数量")
    @PostMapping("/count")
    public ApiResponse<Long> countSessions(
            @Parameter(description = "查询条件", required = true)
            @Valid @RequestBody SessionQueryRequest queryRequest) {
        try {
            log.debug("统计会话数量, 参数: {}", queryRequest);
            Long result = sessionService.countSessions(queryRequest);
            return success(result);
        } catch (Exception e) {
            log.error("统计会话数量失败", e);
            return ApiResponse.error("统计会话数量失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量查询会话", description = "根据ID列表批量查询会话")
    @PostMapping("/batch")
    public ApiResponse<List<Session>> getSessionsByIds(
            @Parameter(description = "会话ID列表", required = true)
            @RequestBody List<String> sessionIds) {
        log.debug("批量查询会话, IDs: {}", sessionIds);
        return ApiResponse.success(sessionService.getSessionsByIds(sessionIds));
    }

    @Operation(summary = "查询热门源IP统计", description = "查询指定时间范围内的热门源IP统计")
    @GetMapping("/statistics/top-source-ips")
    public ApiResponse<List<Map<String, Object>>> getTopSourceIps(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(defaultValue = "10") int limit) {
        log.debug("查询热门源IP统计, 开始时间: {}, 结束时间: {}, 限制: {}", startTime, endTime, limit);
        return ApiResponse.success(sessionService.getTopSourceIps(startTime, endTime, limit));
    }

    @Operation(summary = "查询热门目标IP统计", description = "查询指定时间范围内的热门目标IP统计")
    @GetMapping("/statistics/top-destination-ips")
    public ApiResponse<List<Map<String, Object>>> getTopDestinationIps(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(defaultValue = "10") int limit) {
        log.debug("查询热门目标IP统计, 开始时间: {}, 结束时间: {}, 限制: {}", startTime, endTime, limit);
        return ApiResponse.success(sessionService.getTopDestinationIps(startTime, endTime, limit));
    }

    @Operation(summary = "查询协议分布统计", description = "查询指定时间范围内的协议分布统计")
    @GetMapping("/statistics/protocol-distribution")
    public ApiResponse<List<Map<String, Object>>> getProtocolDistribution(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("查询协议分布统计, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionService.getProtocolDistribution(startTime, endTime));
    }

    @Operation(summary = "查询应用分布统计", description = "查询指定时间范围内的应用分布统计")
    @GetMapping("/statistics/application-distribution")
    public ApiResponse<List<Map<String, Object>>> getApplicationDistribution(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("查询应用分布统计, 开始时间: {}, 结束时间: {}", startTime, endTime);
        return ApiResponse.success(sessionService.getApplicationDistribution(startTime, endTime));
    }

    @Operation(summary = "查询会话趋势", description = "查询指定时间范围内的会话趋势数据")
    @GetMapping("/statistics/trend")
    public ApiResponse<List<Map<String, Object>>> getSessionTrend(
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "时间间隔(分钟)", example = "60")
            @RequestParam(defaultValue = "60") int interval) {
        log.debug("查询会话趋势, 开始时间: {}, 结束时间: {}, 间隔: {}分钟", startTime, endTime, interval);
        return ApiResponse.success(sessionService.getSessionTrend(startTime, endTime, interval));
    }

    @Operation(summary = "搜索会话", description = "根据关键字搜索会话")
    @GetMapping("/search")
    public ApiResponse<Page<SessionListResponse>> searchSessions(
            @Parameter(description = "搜索关键字", required = true)
            @RequestParam String keyword,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") int size) {
        log.debug("搜索会话, 关键字: {}, 页码: {}, 大小: {}", keyword, page, size);
        Page<SessionListResponse> pageParam = new Page<>(page, size);
        return ApiResponse.success(sessionService.searchSessions(keyword, pageParam));
    }
}
