package com.geeksec.session.infrastructure.client;

import com.geeksec.services.common.shared.dto.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP下载任务服务Feign客户端
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@FeignClient(name = "task-service", path = "/api/v1")
public interface PcapDownloadClient {

    /**
     * 创建PCAP下载任务
     * 
     * @param request 下载请求
     * @return 任务结果
     */
    @PostMapping("/pcap-download/create")
    ApiResponse<PcapDownloadTaskResult> createPcapDownloadTask(@RequestBody PcapDownloadRequest request);

    /**
     * 查询下载任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping("/pcap-download/{taskId}/status")
    ApiResponse<PcapDownloadTaskResult> getDownloadTaskStatus(@PathVariable("taskId") Integer taskId);

    /**
     * PCAP下载请求
     */
    record PcapDownloadRequest(
        String userId,
        String alarmType,
        Long alarmTime,
        List<String> sessionIds,
        String description
    ) {}

    /**
     * PCAP下载任务结果
     */
    record PcapDownloadTaskResult(
        Integer taskId,
        String userId,
        String alarmType,
        Long alarmTime,
        List<String> sessionIds,
        String status,
        Integer progress,
        Integer totalFileCount,
        Integer processedFileCount,
        Long archiveFileSize,
        String downloadUrl,
        String errorMessage,
        LocalDateTime createTime,
        LocalDateTime startTime,
        LocalDateTime completeTime,
        LocalDateTime expireTime
    ) {}
}
