package com.geeksec.session.config;

import com.geeksec.services.common.infrastructure.config.CorsConfig;
import com.geeksec.services.common.infrastructure.config.Knife4jConfig;
import com.geeksec.services.common.infrastructure.config.WebMvcBaseConfig;
import com.geeksec.services.common.shared.exception.BaseGlobalExceptionHandler;
import com.geeksec.services.common.infrastructure.interceptor.RequestLogInterceptor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 测试 common 模块配置是否正确加载
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootTest
@SpringJUnitConfig
public class CommonConfigurationTest {

    @Test
    public void testCommonConfigurationLoaded(ApplicationContext context) {
        // 验证 common 模块的配置类是否正确加载
        assertNotNull(context.getBean(CorsConfig.class), "CorsConfig should be loaded");
        assertNotNull(context.getBean(Knife4jConfig.class), "Knife4jConfig should be loaded");
        assertNotNull(context.getBean(WebMvcBaseConfig.class), "WebMvcBaseConfig should be loaded");
        assertNotNull(context.getBean(RequestLogInterceptor.class), "RequestLogInterceptor should be loaded");
    }

    @Test
    public void testGlobalExceptionHandlerLoaded(ApplicationContext context) {
        // 验证全局异常处理器是否正确加载
        assertNotNull(context.getBean("globalExceptionHandler"), "GlobalExceptionHandler should be loaded");
    }
}
