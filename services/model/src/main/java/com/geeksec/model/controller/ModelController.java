package com.geeksec.model.controller;

import com.geeksec.services.common.controller.BaseController;
import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.model.dto.*;
import com.geeksec.model.service.ModelService;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模型管理控制器
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/model")
@RequiredArgsConstructor
@Tag(name = "模型管理", description = "检测模型管理相关接口")
public class ModelController extends BaseController {

    private final ModelService modelService;

    @PostMapping("/page")
    @Operation(summary = "分页查询模型列表", description = "根据条件分页查询模型列表")
    public ApiResponse<Page<ModelResponse>> getModelPage(@RequestBody ModelQueryCondition condition) {
        try {
            Page<ModelResponse> result = modelService.getModelPage(condition);
            return success(result);
        } catch (Exception e) {
            log.error("分页查询模型列表失败", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/list")
    @Operation(summary = "查询模型列表", description = "根据条件查询模型列表")
    public ApiResponse<List<ModelResponse>> getModelList(@RequestBody ModelQueryCondition condition) {
        try {
            List<ModelResponse> result = modelService.getModelList(condition);
            return success(result);
        } catch (Exception e) {
            log.error("查询模型列表失败", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{modelId}")
    @Operation(summary = "查询模型详情", description = "根据模型ID查询模型详情")
    public ApiResponse<ModelResponse> getModelById(
            @Parameter(description = "模型ID", required = true) @PathVariable Integer modelId) {
        try {
            ModelResponse result = modelService.getModelById(modelId);
            if (result == null) {
                return ApiResponse.error("模型不存在");
            }
            return success(result);
        } catch (Exception e) {
            log.error("查询模型详情失败，modelId: {}", modelId, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/name/{modelName}")
    @Operation(summary = "根据名称查询模型", description = "根据模型名称查询模型信息")
    public ApiResponse<ModelResponse> getModelByName(
            @Parameter(description = "模型名称", required = true) @PathVariable String modelName) {
        try {
            ModelResponse result = modelService.getModelByName(modelName);
            if (result == null) {
                return ApiResponse.error("模型不存在");
            }
            return success(result);
        } catch (Exception e) {
            log.error("根据名称查询模型失败，modelName: {}", modelName, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/create")
    @Operation(summary = "创建模型", description = "创建新的检测模型")
    public ApiResponse<Integer> createModel(@Valid @RequestBody ModelCreateRequest request) {
        try {
            // TODO: 从认证上下文获取当前用户
            String operator = "system";
            Integer modelId = modelService.createModel(request, operator);
            return success("创建成功", modelId);
        } catch (Exception e) {
            log.error("创建模型失败", e);
            return ApiResponse.error("创建失败: " + e.getMessage());
        }
    }

    @PutMapping("/update")
    @Operation(summary = "更新模型", description = "更新模型信息")
    public ApiResponse<Boolean> updateModel(@Valid @RequestBody ModelUpdateRequest request) {
        try {
            // TODO: 从认证上下文获取当前用户
            String operator = "system";
            Boolean result = modelService.updateModel(request, operator);
            if (result) {
                return success("更新成功", result);
            } else {
                return ApiResponse.error("更新失败，模型不存在");
            }
        } catch (Exception e) {
            log.error("更新模型失败", e);
            return ApiResponse.error("更新失败: " + e.getMessage());
        }
    }

    @PutMapping("/state")
    @Operation(summary = "更新模型状态", description = "更新模型的启用/禁用状态")
    public ApiResponse<Boolean> updateModelState(@Valid @RequestBody ModelStateUpdateRequest request) {
        try {
            // TODO: 从认证上下文获取当前用户
            String operator = "system";
            Boolean result = modelService.updateModelState(request, operator);
            if (result) {
                return success("状态更新成功", result);
            } else {
                return ApiResponse.error("状态更新失败，模型不存在");
            }
        } catch (Exception e) {
            log.error("更新模型状态失败", e);
            return ApiResponse.error("状态更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{modelId}")
    @Operation(summary = "删除模型", description = "删除指定的模型（软删除）")
    public ApiResponse<Boolean> deleteModel(
            @Parameter(description = "模型ID", required = true) @PathVariable Integer modelId) {
        try {
            // TODO: 从认证上下文获取当前用户
            String operator = "system";
            Boolean result = modelService.deleteModel(modelId, operator);
            if (result) {
                return success("删除成功", result);
            } else {
                return ApiResponse.error("删除失败，模型不存在");
            }
        } catch (Exception e) {
            log.error("删除模型失败，modelId: {}", modelId, e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    @PutMapping("/batch/state")
    @Operation(summary = "批量更新模型状态", description = "批量更新多个模型的状态")
    public ApiResponse<Boolean> batchUpdateModelState(@RequestBody BatchUpdateStateRequest request) {
        try {
            // TODO: 从认证上下文获取当前用户
            String operator = "system";
            Boolean result = modelService.batchUpdateModelState(request.getModelIds(), request.getState(), operator);
            if (result) {
                return success("批量更新成功", result);
            } else {
                return ApiResponse.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新模型状态失败", e);
            return ApiResponse.error("批量更新失败: " + e.getMessage());
        }
    }

    @GetMapping("/state/{state}")
    @Operation(summary = "根据状态查询模型", description = "根据模型状态查询模型列表")
    public ApiResponse<List<ModelResponse>> getModelsByState(
            @Parameter(description = "模型状态", required = true) @PathVariable Integer state) {
        try {
            List<ModelResponse> result = modelService.getModelsByState(state);
            return success(result);
        } catch (Exception e) {
            log.error("根据状态查询模型失败，state: {}", state, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/algorithm/{algorithm}")
    @Operation(summary = "根据算法查询模型", description = "根据算法类型查询模型列表")
    public ApiResponse<List<ModelResponse>> getModelsByAlgorithm(
            @Parameter(description = "算法类型", required = true) @PathVariable String algorithm) {
        try {
            List<ModelResponse> result = modelService.getModelsByAlgorithm(algorithm);
            return success(result);
        } catch (Exception e) {
            log.error("根据算法查询模型失败，algorithm: {}", algorithm, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/type/{modelType}")
    @Operation(summary = "根据类型查询模型", description = "根据模型类型查询模型列表")
    public ApiResponse<List<ModelResponse>> getModelsByType(
            @Parameter(description = "模型类型", required = true) @PathVariable String modelType) {
        try {
            List<ModelResponse> result = modelService.getModelsByType(modelType);
            return success(result);
        } catch (Exception e) {
            log.error("根据类型查询模型失败，modelType: {}", modelType, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/tag/{tag}")
    @Operation(summary = "根据标签查询模型", description = "根据标签查询模型列表")
    public ApiResponse<List<ModelResponse>> getModelsByTag(
            @Parameter(description = "标签", required = true) @PathVariable String tag) {
        try {
            List<ModelResponse> result = modelService.getModelsByTag(tag);
            return success(result);
        } catch (Exception e) {
            log.error("根据标签查询模型失败，tag: {}", tag, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取模型统计信息", description = "获取模型的统计信息")
    public ApiResponse<ModelStatistics> getModelStatistics() {
        try {
            ModelStatistics result = modelService.getModelStatistics();
            return success(result);
        } catch (Exception e) {
            log.error("获取模型统计信息失败", e);
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }

    @GetMapping("/validate/name")
    @Operation(summary = "验证模型名称", description = "验证模型名称是否已存在")
    public ApiResponse<Boolean> validateModelName(
            @Parameter(description = "模型名称", required = true) @RequestParam String modelName,
            @Parameter(description = "排除的模型ID") @RequestParam(required = false) Integer excludeId) {
        try {
            Boolean exists = modelService.isModelNameExists(modelName, excludeId);
            return success(exists ? "模型名称已存在" : "模型名称可用", !exists);
        } catch (Exception e) {
            log.error("验证模型名称失败，modelName: {}", modelName, e);
            return ApiResponse.error("验证失败: " + e.getMessage());
        }
    }

    @PostMapping("/validate/config")
    @Operation(summary = "验证模型配置", description = "验证模型配置JSON格式是否正确")
    public ApiResponse<Boolean> validateModelConfig(@RequestBody String modelConfig) {
        try {
            Boolean valid = modelService.validateModelConfig(modelConfig);
            return success(valid ? "配置格式正确" : "配置格式错误", valid);
        } catch (Exception e) {
            log.error("验证模型配置失败", e);
            return ApiResponse.error("验证失败: " + e.getMessage());
        }
    }
}
