package com.geeksec.knowledgebase.controller;

import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.knowledgebase.util.GeoIpDatabaseManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import cn.dev33.satoken.annotation.SaCheckRole;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping("/admin/geoip")
@RequiredArgsConstructor
@Tag(name = "GeoIP Admin Controller", description = "GeoIP数据库管理")
@SaCheckRole("ADMIN")
public class GeoIpAdminController {

    private final GeoIpDatabaseManager geoIpDatabaseManager;

    @GetMapping("/info")
    @Operation(summary = "获取GeoIP数据库信息")
    public ApiResponse<String> getDatabaseInfo() {
        String info = geoIpDatabaseManager.getDatabaseInfo();
        return ApiResponse.success(info);
    }

    @PostMapping("/upload")
    @Operation(summary = "通过文件上传更新GeoIP数据库")
    public ApiResponse<Boolean> uploadDatabase(
            @Parameter(description = "城市数据库文件 (GeoLite2-City.mmdb)") @RequestParam("cityFile") MultipartFile cityFile,
            @Parameter(description = "ASN数据库文件 (GeoLite2-ASN.mmdb)") @RequestParam("asnFile") MultipartFile asnFile) {
        log.info("收到GeoIP数据库更新请求");
        boolean success = geoIpDatabaseManager.updateDatabase(cityFile, asnFile);
        if (success) {
            return ApiResponse.success(true, "数据库更新成功");
        } else {
            return ApiResponse.error("数据库更新失败");
        }
    }

    @PostMapping("/reload")
    @Operation(summary = "手动触发重新加载GeoIP数据库")
    public ApiResponse<Boolean> reloadDatabase() {
        log.info("收到手动重新加载GeoIP数据库请求");
        boolean success = geoIpDatabaseManager.reloadDatabases();
        if (success) {
            return ApiResponse.success(true, "数据库重新加载成功");
        } else {
            return ApiResponse.error("数据库重新加载失败");
        }
    }
}
