package com.geeksec.knowledgebase.controller;

import com.geeksec.services.common.shared.controller.BaseController;
import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.knowledgebase.service.KnowledgeBaseManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import cn.dev33.satoken.annotation.SaCheckRole;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/kb/management")
@RequiredArgsConstructor
@Tag(name = "KnowledgeBase Management Controller", description = "知识库管理")
@SaCheckRole("ADMIN")
public class KnowledgeBaseManagementController extends BaseController {

    private final KnowledgeBaseManagementService knowledgeBaseManagementService;

    @PostMapping("/reload-all")
    @Operation(summary = "重新加载所有知识库", description = "触发所有知识库的重新加载")
    public ApiResponse<Map<String, String>> reloadAllKnowledgeBases() {
        log.info("开始重新加载所有知识库");
        Map<String, String> status = knowledgeBaseManagementService.reloadAll();
        log.info("所有知识库重新加载完成");
        return ApiResponse.success(status);
    }

    @PostMapping("/status")
    @Operation(summary = "获取知识库加载状态", description = "获取各个知识库的加载状态和信息")
    public ApiResponse<Map<String, Object>> getKnowledgeBaseStatus() {
        log.debug("获取知识库加载状态");
        Map<String, Object> status = knowledgeBaseManagementService.getStatus();
        return ApiResponse.success(status);
    }
}
