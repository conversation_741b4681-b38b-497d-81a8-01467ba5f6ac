package com.geeksec.auth.interfaces.rest;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.auth.application.dto.command.CreateRoleCommand;
import com.geeksec.auth.application.dto.command.UpdateRoleCommand;
import com.geeksec.auth.application.dto.response.RoleResponseDto;
import com.geeksec.auth.application.service.RoleApplicationService;
import com.geeksec.services.common.shared.dto.ApiResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 角色控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/roles")
@RequiredArgsConstructor
@Tag(name = "角色管理", description = "角色管理相关接口")
public class RoleController {
    private final RoleApplicationService roleApplicationService;

    @PostMapping
    @Operation(summary = "创建角色")
    public ResponseEntity<ApiResponse<RoleResponseDto>> createRole(@Valid @RequestBody CreateRoleCommand command) {
        RoleResponseDto role = roleApplicationService.createRole(command);
        return ResponseEntity.ok(ApiResponse.success(role));
    }

    @GetMapping("/{roleId}")
    @Operation(summary = "获取角色")
    public ResponseEntity<ApiResponse<RoleResponseDto>> getRole(@PathVariable Long roleId) {
        return roleApplicationService.getRole(roleId)
                .map(role -> ResponseEntity.ok(ApiResponse.success(role)))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping
    @Operation(summary = "获取所有角色")
    public ResponseEntity<ApiResponse<List<RoleResponseDto>>> getAllRoles() {
        List<RoleResponseDto> roles = roleApplicationService.getAllRoles();
        return ResponseEntity.ok(ApiResponse.success(roles));
    }

    @PutMapping("/{roleId}")
    @Operation(summary = "更新角色")
    public ResponseEntity<ApiResponse<RoleResponseDto>> updateRole(
            @PathVariable Long roleId,
            @Valid @RequestBody UpdateRoleCommand command) {
        return roleApplicationService.updateRole(roleId, command)
                .map(role -> ResponseEntity.ok(ApiResponse.success(role)))
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{roleId}")
    @Operation(summary = "删除角色")
    public ResponseEntity<ApiResponse<Void>> deleteRole(@PathVariable Long roleId) {
        if (roleApplicationService.deleteRole(roleId)) {
            return ResponseEntity.ok(ApiResponse.success(null));
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除角色")
    public ResponseEntity<ApiResponse<Void>> batchDeleteRoles(@RequestBody List<Long> roleIds) {
        if (roleApplicationService.batchDeleteRoles(roleIds)) {
            return ResponseEntity.ok(ApiResponse.success(null));
        }
        return ResponseEntity.badRequest().build();
    }

    @PutMapping("/{roleId}/permissions")
    @Operation(summary = "分配权限")
    public ResponseEntity<ApiResponse<Void>> assignPermissions(
            @PathVariable Long roleId,
            @RequestBody List<Long> permissionIds) {
        if (roleApplicationService.assignPermissions(roleId, permissionIds)) {
            return ResponseEntity.ok(ApiResponse.success(null));
        }
        return ResponseEntity.notFound().build();
    }
}
