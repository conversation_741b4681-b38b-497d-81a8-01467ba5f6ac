package com.geeksec.auth.interfaces.rest;

import com.geeksec.auth.application.dto.command.CreatePermissionCommand;
import com.geeksec.auth.application.dto.command.UpdatePermissionCommand;
import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.auth.application.dto.response.PermissionResponseDto;
import com.geeksec.auth.application.service.PermissionApplicationService;
import com.geeksec.services.common.shared.validation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 权限控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/permissions")
@Tag(name = "权限接口")
public class PermissionController {
    private final PermissionApplicationService permissionApplicationService;

    public PermissionController(PermissionApplicationService permissionApplicationService) {
        this.permissionApplicationService = permissionApplicationService;
    }

    /**
     * 获取所有权限
     */
    @GetMapping
    @Operation(summary = "获取所有权限")
    @SaCheckPermission("permission:read")
    @OperationLog(module = "权限", operation = "查询", description = "获取所有权限")
    public ResponseEntity<ApiResponse<List<PermissionResponseDto>>> getAllPermissions() {
        List<PermissionResponseDto> permissions = permissionApplicationService.getAllPermissions();
        return ResponseEntity.ok(ApiResponse.success(permissions));
    }

    /**
     * 获取权限
     */
    @GetMapping("/{permissionId}")
    @Operation(summary = "获取权限")
    @SaCheckPermission("permission:read")
    @OperationLog(module = "权限", operation = "查询", description = "获取权限详情")
    public ResponseEntity<ApiResponse<PermissionResponseDto>> getPermission(@PathVariable Long permissionId) {
        return permissionApplicationService.getPermission(permissionId)
                .map(permission -> ResponseEntity.ok(ApiResponse.success(permission)))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 创建权限
     */
    @PostMapping
    @Operation(summary = "创建权限")
    @SaCheckPermission("permission:create")
    @OperationLog(module = "权限", operation = "创建", description = "创建新权限")
    public ResponseEntity<ApiResponse<PermissionResponseDto>> createPermission(@Valid @RequestBody CreatePermissionCommand command) {
        PermissionResponseDto createdPermission = permissionApplicationService.createPermission(command);
        return ResponseEntity.ok(ApiResponse.success(createdPermission));
    }

    /**
     * 更新权限
     */
    @PutMapping("/{permissionId}")
    @Operation(summary = "更新权限")
    @SaCheckPermission("permission:update")
    @OperationLog(module = "权限", operation = "更新", description = "更新权限信息")
    public ResponseEntity<ApiResponse<PermissionResponseDto>> updatePermission(@PathVariable Long permissionId, @Valid @RequestBody UpdatePermissionCommand command) {
        return permissionApplicationService.updatePermission(permissionId, command)
                .map(permission -> ResponseEntity.ok(ApiResponse.success(permission)))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{permissionId}")
    @Operation(summary = "删除权限")
    @SaCheckPermission("permission:delete")
    @OperationLog(module = "权限", operation = "删除", description = "删除权限")
    public ResponseEntity<ApiResponse<Map<String, Object>>> deletePermission(@PathVariable Long permissionId) {
        boolean deleted = permissionApplicationService.deletePermission(permissionId);
        return ResponseEntity.ok(ApiResponse.success(Map.of("success", deleted)));
    }
}
