package com.geeksec.auth.application.service;

import java.util.Optional;

import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.geeksec.auth.application.dto.command.CreateUserCommand;
import com.geeksec.auth.application.dto.command.UpdateUserCommand;
import com.geeksec.auth.application.dto.response.UserResponseDto;
import com.geeksec.auth.domain.event.UserCreatedEvent;
import com.geeksec.auth.domain.model.User;
import com.geeksec.auth.domain.repository.UserRepository;
import com.geeksec.services.common.domain.event.DomainEventPublisher;

/**
 * 用户应用服务
 *
 * <AUTHOR>
 */
@Service
public class UserApplicationService {
    private final UserRepository userRepository;
    private final DomainEventPublisher eventPublisher;
    private final User.PasswordEncoder passwordEncoder;

    public UserApplicationService(
            UserRepository userRepository,
            DomainEventPublisher eventPublisher,
            User.PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.eventPublisher = eventPublisher;
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * 创建用户
     */
    @Transactional(rollbackFor = Exception.class)
    public UserResponseDto createUser(CreateUserCommand command) {
        // 检查用户名是否存在
        if (userRepository.existsByUsername(new User.Username(command.username()))) {
            throw new RuntimeException("用户名已存在");
        }

        // 创建用户
        User user = new User.Builder()
                .withUsername(new User.Username(command.username()))
                .withPassword(User.Password.fromRaw(command.password(), passwordEncoder))
                .withDisplayName(command.displayName())
                .withStatus(User.UserStatus.ACTIVE)
                .build();

        // 保存用户
        User savedUser = userRepository.save(user);

        // 发布事件
        eventPublisher.publish(new UserCreatedEvent(savedUser.getId(), savedUser.getUsername()));

        // 返回DTO
        return toResponseDto(savedUser);
    }

    /**
     * 获取用户
     */
    public Optional<UserResponseDto> getUser(Long userId) {
        return userRepository.findById(new User.UserId(userId))
                .map(this::toResponseDto);
    }

    /**
     * 获取所有用户
     */
    public org.springframework.data.domain.Page<UserResponseDto> getAllUsers(Pageable pageable) {
        org.springframework.data.domain.Page<User> users = userRepository.findAll(pageable);
        return users.map(this::toResponseDto);
    }

    /**
     * 更新用户
     */
    @Transactional(rollbackFor = Exception.class)
    public Optional<UserResponseDto> updateUser(Long userId, UpdateUserCommand command) {
        return userRepository.findById(new User.UserId(userId))
                .map(user -> {
                    // 更新用户信息
                    User updatedUser = new User.Builder()
                            .withId(user.getId())
                            .withUsername(user.getUsername())
                            .withPassword(user.getPassword())
                            .withDisplayName(command.displayName())
                            .withStatus(command.status() ? User.UserStatus.ACTIVE : User.UserStatus.INACTIVE)
                            .withRoles(user.getRoles())
                            .withCreatedAt(user.getCreatedAt())
                            .build();

                    // 保存用户
                    return userRepository.save(updatedUser);
                })
                .map(this::toResponseDto);
    }

    /**
     * 删除用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long userId) {
        return userRepository.findById(new User.UserId(userId))
                .map(user -> {
                    userRepository.delete(user);
                    return true;
                })
                .orElse(false);
    }

    /**
     * 修改密码
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        return userRepository.findById(new User.UserId(userId))
                .map(user -> {
                    // 验证旧密码
                    if (!user.authenticate(oldPassword)) {
                        throw new RuntimeException("原密码错误");
                    }

                    // 更新密码
                    user.changePassword(User.Password.fromRaw(newPassword, passwordEncoder));

                    // 保存用户
                    userRepository.save(user);

                    return true;
                })
                .orElseThrow(() -> new RuntimeException("用户不存在"));
    }

    /**
     * 将领域模型转换为响应DTO
     */
    private UserResponseDto toResponseDto(User user) {
        return new UserResponseDto(
                user.getId().getValue(),
                user.getUsername().getValue(),
                user.getDisplayName(),
                user.getStatus() == User.UserStatus.ACTIVE
        );
    }
}
