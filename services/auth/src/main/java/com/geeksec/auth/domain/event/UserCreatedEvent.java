package com.geeksec.auth.domain.event;

import com.geeksec.auth.domain.model.User;
import com.geeksec.services.common.domain.event.AbstractDomainEvent;
import lombok.Getter;

/**
 * 用户创建事件
 *
 * <AUTHOR>
 */
@Getter
public class UserCreatedEvent extends AbstractDomainEvent {

    private final User.UserId userId;
    private final User.Username username;

    public UserCreatedEvent(User.UserId userId, User.Username username) {
        super(userId.toString());
        this.userId = userId;
        this.username = username;
    }
}
