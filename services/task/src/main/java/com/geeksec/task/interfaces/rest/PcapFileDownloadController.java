package com.geeksec.task.interfaces.rest;

import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.task.application.service.impl.PcapCleanupServiceImpl;
import com.geeksec.task.model.entity.PcapDownloadTask;
import com.geeksec.task.repository.PcapDownloadTaskRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP文件下载控制器
 * 提供PCAP文件的下载服务
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/pcap/files")
@RequiredArgsConstructor
@Tag(name = "PCAP文件下载", description = "PCAP文件下载相关接口")
public class PcapFileDownloadController {

    private final PcapDownloadTaskRepository pcapDownloadTaskRepository;
    private final PcapCleanupServiceImpl pcapCleanupService;

    /**
     * PCAP下载文件存储路径
     */
    @Value("${task.file.pcap-download-path:/data/download/pcap/}")
    private String pcapDownloadPath;

    /**
     * 下载PCAP文件
     */
    @GetMapping("/download/{taskId}/{fileName}")
    @Operation(summary = "下载PCAP文件", description = "根据任务ID和文件名下载PCAP文件")
    public ResponseEntity<Resource> downloadPcapFile(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId,
            @Parameter(description = "文件名", required = true)
            @PathVariable String fileName) {
        
        try {
            log.info("下载PCAP文件请求: taskId={}, fileName={}", taskId, fileName);

            // 验证任务是否存在且已完成
            PcapDownloadTask task = pcapDownloadTaskRepository.selectOneById(taskId);
            if (task == null) {
                log.warn("任务不存在: taskId={}", taskId);
                return ResponseEntity.notFound().build();
            }

            if (task.getStatus() != PcapDownloadTask.TaskStatus.COMPLETED.getCode()) {
                log.warn("任务未完成: taskId={}, status={}", taskId, task.getStatus());
                return ResponseEntity.badRequest().build();
            }

            // 检查文件是否过期
            if (task.getExpireTime() != null && task.getExpireTime().isBefore(LocalDateTime.now())) {
                log.warn("文件已过期: taskId={}, expireTime={}", taskId, task.getExpireTime());
                return ResponseEntity.status(HttpStatus.GONE).build();
            }

            // 构建文件路径
            Path filePath = Paths.get(pcapDownloadPath, taskId.toString(), fileName);
            File file = filePath.toFile();

            if (!file.exists() || !file.isFile()) {
                log.warn("文件不存在: path={}", filePath);
                return ResponseEntity.notFound().build();
            }

            // 安全检查：确保文件在允许的目录内
            if (!isFileInAllowedDirectory(filePath)) {
                log.warn("文件路径不安全: path={}", filePath);
                return ResponseEntity.badRequest().build();
            }

            // 创建文件资源
            Resource resource = new FileSystemResource(file);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));

            log.info("开始下载文件: taskId={}, fileName={}, size={}", taskId, fileName, file.length());

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);

        } catch (Exception e) {
            log.error("下载PCAP文件失败: taskId={}, fileName={}", taskId, fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取任务的文件列表
     */
    @GetMapping("/files/{taskId}")
    @Operation(summary = "获取任务文件列表", description = "获取指定任务的所有可下载文件")
    public ApiResponse<List<FileInfo>> getTaskFiles(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId) {
        
        try {
            log.debug("获取任务文件列表: taskId={}", taskId);

            // 验证任务是否存在
            PcapDownloadTask task = pcapDownloadTaskRepository.selectOneById(taskId);
            if (task == null) {
                return ApiResponse.error("任务不存在");
            }

            // 获取任务目录下的文件
            Path taskDir = Paths.get(pcapDownloadPath, taskId.toString());
            if (!Files.exists(taskDir) || !Files.isDirectory(taskDir)) {
                return ApiResponse.success(List.of());
            }

            List<FileInfo> fileInfos = Files.list(taskDir)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".pcap"))
                    .map(this::createFileInfo)
                    .toList();

            return ApiResponse.success(fileInfos);

        } catch (Exception e) {
            log.error("获取任务文件列表失败: taskId={}", taskId, e);
            return ApiResponse.error("获取文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取清理统计信息
     */
    @GetMapping("/cleanup/stats")
    @Operation(summary = "获取清理统计信息", description = "获取PCAP文件清理的统计信息")
    public ApiResponse<PcapCleanupServiceImpl.CleanupStats> getCleanupStats() {
        try {
            PcapCleanupServiceImpl.CleanupStats stats = pcapCleanupService.getCleanupStats();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取清理统计信息失败", e);
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 手动清理用户过期任务
     */
    @PostMapping("/cleanup/user/{userId}")
    @Operation(summary = "清理用户过期任务", description = "手动清理指定用户的过期PCAP下载任务")
    public ApiResponse<Integer> cleanupUserExpiredTasks(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId) {
        
        try {
            log.info("手动清理用户过期任务: userId={}", userId);
            int cleanedCount = pcapCleanupService.cleanupUserExpiredTasks(userId);
            return ApiResponse.success(cleanedCount);
        } catch (Exception e) {
            log.error("清理用户过期任务失败: userId={}", userId, e);
            return ApiResponse.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件是否在允许的目录内
     * 
     * @param filePath 文件路径
     * @return 是否安全
     */
    private boolean isFileInAllowedDirectory(Path filePath) {
        try {
            Path allowedDir = Paths.get(pcapDownloadPath).toRealPath();
            Path realFilePath = filePath.toRealPath();
            return realFilePath.startsWith(allowedDir);
        } catch (Exception e) {
            log.warn("检查文件路径安全性失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 创建文件信息对象
     * 
     * @param path 文件路径
     * @return 文件信息
     */
    private FileInfo createFileInfo(Path path) {
        try {
            String fileName = path.getFileName().toString();
            long fileSize = Files.size(path);
            LocalDateTime lastModified = LocalDateTime.ofInstant(
                    Files.getLastModifiedTime(path).toInstant(),
                    java.time.ZoneId.systemDefault()
            );

            return new FileInfo(fileName, fileSize, lastModified);

        } catch (Exception e) {
            log.warn("创建文件信息失败: {}", path, e);
            return new FileInfo(path.getFileName().toString(), 0L, LocalDateTime.now());
        }
    }

    /**
     * 文件信息
     */
    public record FileInfo(
        String fileName,
        Long fileSize,
        LocalDateTime lastModified
    ) {}

    /**
     * 检查任务状态
     */
    @GetMapping("/status/{taskId}")
    @Operation(summary = "检查任务状态", description = "检查PCAP下载任务的当前状态")
    public ApiResponse<TaskStatusInfo> getTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId) {
        
        try {
            PcapDownloadTask task = pcapDownloadTaskRepository.selectOneById(taskId);
            if (task == null) {
                return ApiResponse.error("任务不存在");
            }

            TaskStatusInfo statusInfo = new TaskStatusInfo(
                task.getId(),
                PcapDownloadTask.TaskStatus.fromCode(task.getStatus()).name(),
                task.getProgress(),
                task.getTotalFileCount(),
                task.getProcessedFileCount(),
                task.getArchiveFileSize(),
                task.getDownloadUrl(),
                task.getErrorMessage(),
                task.getCreateTime(),
                task.getStartTime(),
                task.getCompleteTime(),
                task.getExpireTime()
            );

            return ApiResponse.success(statusInfo);

        } catch (Exception e) {
            log.error("获取任务状态失败: taskId={}", taskId, e);
            return ApiResponse.error("获取任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 任务状态信息
     */
    public record TaskStatusInfo(
        Integer taskId,
        String status,
        Integer progress,
        Integer totalFileCount,
        Integer processedFileCount,
        Long archiveFileSize,
        String downloadUrl,
        String errorMessage,
        LocalDateTime createTime,
        LocalDateTime startTime,
        LocalDateTime completeTime,
        LocalDateTime expireTime
    ) {}
}
