package com.geeksec.task.interfaces.controller;

import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.task.application.service.ImportTaskScheduler;
import com.geeksec.task.application.service.PcapImportManagerService;
import com.geeksec.task.application.service.ServiceStatusManager;
import com.geeksec.task.application.service.TaskBatchManagerService;
import com.geeksec.task.model.dto.BatchOperationResultDto;
import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;
import com.geeksec.task.model.vo.ImportServiceStatusVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * PCAP 导入管理控制器
 * 提供 REST API 接口，支持任务创建、状态查询、批次管理等操作
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/pcap-import")
@RequiredArgsConstructor
@Validated
@Tag(name = "PCAP导入管理", description = "PCAP文件导入任务的管理操作")
public class PcapImportController {

    private final PcapImportManagerService pcapImportManagerService;
    private final ImportTaskScheduler importTaskScheduler;
    private final ServiceStatusManager serviceStatusManager;
    private final TaskBatchManagerService taskBatchManagerService;

    /**
     * 提交PCAP导入任务
     */
    @PostMapping("/submit")
    @Operation(summary = "提交PCAP导入任务", description = "提交新的PCAP文件导入任务到调度队列")
    public ApiResponse<PcapImportResultDto> submitImportTask(
            @Parameter(description = "导入任务请求", required = true)
            @RequestBody @Valid PcapImportRequestDto request) {
        try {
            log.info("接收到PCAP导入任务提交请求，任务ID: {}, 批次ID: {}", request.getTaskId(), request.getBatchId());
            
            PcapImportResultDto result = importTaskScheduler.submitImportTask(request);
            
            if (result.getSuccess()) {
                return ApiResponse.success(result);
            } else {
                return ApiResponse.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("提交PCAP导入任务失败", e);
            return ApiResponse.error(500, "提交任务失败：" + e.getMessage());
        }
    }

    /**
     * 直接处理PCAP导入任务（不经过队列）
     */
    @PostMapping("/process")
    @Operation(summary = "直接处理PCAP导入任务", description = "直接处理PCAP导入任务，不经过调度队列")
    public ApiResponse<PcapImportResultDto> processImportTask(
            @Parameter(description = "导入任务请求", required = true)
            @RequestBody @Valid PcapImportRequestDto request) {
        try {
            log.info("接收到直接处理PCAP导入任务请求，任务ID: {}, 批次ID: {}", request.getTaskId(), request.getBatchId());
            
            PcapImportResultDto result = pcapImportManagerService.processImportRequest(request);
            
            if (result.getSuccess()) {
                return ApiResponse.success(result);
            } else {
                return ApiResponse.error(500, result.getMessage());
            }
        } catch (Exception e) {
            log.error("直接处理PCAP导入任务失败", e);
            return ApiResponse.error(500, "处理任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有服务实例状态
     */
    @GetMapping("/services/status")
    @Operation(summary = "获取服务状态", description = "获取所有PCAP导入服务实例的状态信息")
    public ApiResponse<List<ImportServiceStatusVo>> getAllServiceStatus() {
        try {
            List<ImportServiceStatusVo> statusList = serviceStatusManager.getAllServiceStatus();
            return ApiResponse.success(statusList);
        } catch (Exception e) {
            log.error("获取服务状态失败", e);
            return ApiResponse.error(500, "获取服务状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定服务实例状态
     */
    @GetMapping("/services/{serviceId}/status")
    @Operation(summary = "获取指定服务状态", description = "获取指定服务实例的状态信息")
    public ApiResponse<ImportServiceStatusVo> getServiceStatus(
            @Parameter(description = "服务实例ID", required = true)
            @PathVariable Integer serviceId) {
        try {
            ImportServiceStatusVo status = serviceStatusManager.getServiceStatus(serviceId);
            if (status != null) {
                return ApiResponse.success(status);
            } else {
                return ApiResponse.error(404, "服务实例不存在：" + serviceId);
            }
        } catch (Exception e) {
            log.error("获取服务状态失败，服务ID: {}", serviceId, e);
            return ApiResponse.error(500, "获取服务状态失败：" + e.getMessage());
        }
    }

    /**
     * 停止指定服务实例
     */
    @PostMapping("/services/{serviceId}/stop")
    @Operation(summary = "停止服务实例", description = "停止指定的PCAP导入服务实例")
    public ApiResponse<Void> stopService(
            @Parameter(description = "服务实例ID", required = true)
            @PathVariable Integer serviceId) {
        try {
            boolean stopped = serviceStatusManager.stopService(serviceId);
            if (stopped) {
                return ApiResponse.<Void>success("服务实例停止成功", null);
            } else {
                return ApiResponse.error(500, "停止服务实例失败");
            }
        } catch (Exception e) {
            log.error("停止服务实例失败，服务ID: {}", serviceId, e);
            return ApiResponse.error(500, "停止服务实例失败：" + e.getMessage());
        }
    }

    /**
     * 重启指定服务实例
     */
    @PostMapping("/services/{serviceId}/restart")
    @Operation(summary = "重启服务实例", description = "重启指定的PCAP导入服务实例")
    public ApiResponse<Void> restartService(
            @Parameter(description = "服务实例ID", required = true)
            @PathVariable Integer serviceId) {
        try {
            boolean restarted = serviceStatusManager.createOrRestartService(serviceId);
            if (restarted) {
                return ApiResponse.<Void>success("服务实例重启成功", null);
            } else {
                return ApiResponse.error(500, "重启服务实例失败");
            }
        } catch (Exception e) {
            log.error("重启服务实例失败，服务ID: {}", serviceId, e);
            return ApiResponse.error(500, "重启服务实例失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务调度器状态
     */
    @GetMapping("/scheduler/status")
    @Operation(summary = "获取调度器状态", description = "获取任务调度器的状态信息")
    public ApiResponse<ImportTaskScheduler.SchedulerStatus> getSchedulerStatus() {
        try {
            ImportTaskScheduler.SchedulerStatus status = importTaskScheduler.getSchedulerStatus();
            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("获取调度器状态失败", e);
            return ApiResponse.error(500, "获取调度器状态失败：" + e.getMessage());
        }
    }

    /**
     * 启动任务调度器
     */
    @PostMapping("/scheduler/start")
    @Operation(summary = "启动调度器", description = "启动任务调度器")
    public ApiResponse<Void> startScheduler() {
        try {
            importTaskScheduler.startScheduler();
            return ApiResponse.<Void>success("任务调度器启动成功", null);
        } catch (Exception e) {
            log.error("启动任务调度器失败", e);
            return ApiResponse.error(500, "启动任务调度器失败：" + e.getMessage());
        }
    }

    /**
     * 停止任务调度器
     */
    @PostMapping("/scheduler/stop")
    @Operation(summary = "停止调度器", description = "停止任务调度器")
    public ApiResponse<Void> stopScheduler() {
        try {
            importTaskScheduler.stopScheduler();
            return ApiResponse.<Void>success("任务调度器停止成功", null);
        } catch (Exception e) {
            log.error("停止任务调度器失败", e);
            return ApiResponse.error(500, "停止任务调度器失败：" + e.getMessage());
        }
    }

    /**
     * 取消导入任务
     */
    @PostMapping("/tasks/{taskId}/batches/{batchId}/cancel")
    @Operation(summary = "取消导入任务", description = "取消指定的PCAP导入任务")
    public ApiResponse<Void> cancelImportTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId,
            @Parameter(description = "批次ID", required = true)
            @PathVariable Integer batchId) {
        try {
            boolean cancelled = importTaskScheduler.cancelImportTask(taskId, batchId);
            if (cancelled) {
                return ApiResponse.<Void>success("任务取消成功", null);
            } else {
                return ApiResponse.error(400, "任务取消失败，任务可能不存在或已完成");
            }
        } catch (Exception e) {
            log.error("取消导入任务失败，任务ID: {}, 批次ID: {}", taskId, batchId, e);
            return ApiResponse.error(500, "取消任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取待处理任务列表
     */
    @GetMapping("/tasks/pending")
    @Operation(summary = "获取待处理任务", description = "获取调度队列中待处理的任务列表")
    public ApiResponse<List<PcapImportRequestDto>> getPendingTasks() {
        try {
            List<PcapImportRequestDto> pendingTasks = importTaskScheduler.getPendingTasks();
            return ApiResponse.success(pendingTasks);
        } catch (Exception e) {
            log.error("获取待处理任务失败", e);
            return ApiResponse.error(500, "获取待处理任务失败：" + e.getMessage());
        }
    }

    /**
     * 清空任务队列
     */
    @PostMapping("/tasks/clear")
    @Operation(summary = "清空任务队列", description = "清空调度队列中的所有待处理任务")
    public ApiResponse<Integer> clearTaskQueue() {
        try {
            int clearedCount = importTaskScheduler.clearTaskQueue();
            return ApiResponse.success("任务队列清空成功", clearedCount);
        } catch (Exception e) {
            log.error("清空任务队列失败", e);
            return ApiResponse.error(500, "清空任务队列失败：" + e.getMessage());
        }
    }

    /**
     * 停止批次处理
     */
    @PostMapping("/batches/{batchId}/stop")
    @Operation(summary = "停止批次处理", description = "停止指定批次的处理")
    public ApiResponse<BatchOperationResultDto> stopBatch(
            @Parameter(description = "批次ID", required = true)
            @PathVariable Integer batchId) {
        try {
            BatchOperationResultDto result = taskBatchManagerService.stopBatch(batchId);
            if (result.getSuccess()) {
                return ApiResponse.success(result);
            } else {
                return ApiResponse.error(500, result.getMessage(), result);
            }
        } catch (Exception e) {
            log.error("停止批次处理失败，批次ID: {}", batchId, e);
            return ApiResponse.error(500, "停止批次处理失败：" + e.getMessage());
        }
    }

    /**
     * 重启批次处理
     */
    @PostMapping("/batches/{batchId}/restart")
    @Operation(summary = "重启批次处理", description = "重启指定批次的处理")
    public ApiResponse<BatchOperationResultDto> restartBatch(
            @Parameter(description = "批次ID", required = true)
            @PathVariable Integer batchId) {
        try {
            BatchOperationResultDto result = taskBatchManagerService.restartBatch(batchId);
            if (result.getSuccess()) {
                return ApiResponse.success(result);
            } else {
                return ApiResponse.error(500, result.getMessage(), result);
            }
        } catch (Exception e) {
            log.error("重启批次处理失败，批次ID: {}", batchId, e);
            return ApiResponse.error(500, "重启批次处理失败：" + e.getMessage());
        }
    }

    /**
     * 删除批次
     */
    @DeleteMapping("/batches/{batchId}")
    @Operation(summary = "删除批次", description = "删除指定的批次")
    public ApiResponse<BatchOperationResultDto> deleteBatch(
            @Parameter(description = "批次ID", required = true)
            @PathVariable Integer batchId) {
        try {
            BatchOperationResultDto result = taskBatchManagerService.deleteBatch(batchId);
            if (result.getSuccess()) {
                return ApiResponse.success(result);
            } else {
                return ApiResponse.error(500, result.getMessage(), result);
            }
        } catch (Exception e) {
            log.error("删除批次失败，批次ID: {}", batchId, e);
            return ApiResponse.error(500, "删除批次失败：" + e.getMessage());
        }
    }

    /**
     * 获取批次进度
     */
    @GetMapping("/batches/{batchId}/progress")
    @Operation(summary = "获取批次进度", description = "获取指定批次的处理进度")
    public ApiResponse<Double> getBatchProgress(
            @Parameter(description = "批次ID", required = true)
            @PathVariable Integer batchId) {
        try {
            Double progress = taskBatchManagerService.getBatchProgress(batchId);
            return ApiResponse.success(progress);
        } catch (Exception e) {
            log.error("获取批次进度失败，批次ID: {}", batchId, e);
            return ApiResponse.error(500, "获取批次进度失败：" + e.getMessage());
        }
    }

    /**
     * 检查批次是否完成
     */
    @GetMapping("/batches/{batchId}/completed")
    @Operation(summary = "检查批次完成状态", description = "检查指定批次是否已完成处理")
    public ApiResponse<Boolean> isBatchCompleted(
            @Parameter(description = "批次ID", required = true)
            @PathVariable Integer batchId) {
        try {
            boolean completed = taskBatchManagerService.isBatchCompleted(batchId);
            return ApiResponse.success(completed);
        } catch (Exception e) {
            log.error("检查批次完成状态失败，批次ID: {}", batchId, e);
            return ApiResponse.error("检查批次完成状态失败：" + e.getMessage());
        }
    }
}
