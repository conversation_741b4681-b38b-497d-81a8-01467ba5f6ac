package com.geeksec.task.interfaces.rest;

import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.task.application.service.PcapDownloadService;
import com.geeksec.task.application.service.PcapDownloadService.PcapDownloadRequest;
import com.geeksec.task.application.service.PcapDownloadService.PcapDownloadTaskResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * PCAP任务统一控制器
 * 提供PCAP下载任务的统一入口，采用事件驱动架构
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/pcap")
@RequiredArgsConstructor
@Tag(name = "PCAP任务管理", description = "PCAP下载任务的统一管理接口")
public class PcapTaskController {

    private final PcapDownloadService pcapDownloadService;

    /**
     * 创建PCAP下载任务（统一入口）
     */
    @PostMapping("/tasks")
    @Operation(summary = "创建PCAP下载任务", description = "创建异步PCAP下载任务，立即返回任务ID")
    public ApiResponse<PcapDownloadTaskResult> createTask(
            @Parameter(description = "PCAP下载请求", required = true)
            @Valid @RequestBody PcapDownloadRequest request) {
        try {
            log.info("创建PCAP下载任务: userId={}, sessionCount={}",
                    request.userId(), request.sessionIds().size());

            // 统一的任务创建入口，采用事件驱动架构
            PcapDownloadTaskResult result = pcapDownloadService.createPcapDownloadTask(request);

            return ApiResponse.success("任务创建成功，正在后台处理", result);
        } catch (Exception e) {
            log.error("创建PCAP下载任务失败", e);
            return ApiResponse.error("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/tasks/{taskId}")
    @Operation(summary = "查询任务状态", description = "查询PCAP下载任务的当前状态和进度")
    public ApiResponse<PcapDownloadTaskResult> getTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId,
            @Parameter(description = "用户ID")
            @RequestParam(required = false) String userId) {
        try {
            PcapDownloadTaskResult status = pcapDownloadService.getDownloadTaskStatus(taskId);
            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("查询任务状态失败: taskId={}", taskId, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 取消任务
     */
    @PostMapping("/tasks/{taskId}/cancel")
    @Operation(summary = "取消任务", description = "取消正在进行的PCAP下载任务")
    public ApiResponse<String> cancelTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId,
            @Parameter(description = "用户ID", required = true)
            @RequestParam String userId) {
        try {
            boolean success = pcapDownloadService.cancelDownloadTask(taskId, userId);
            if (success) {
                return ApiResponse.success("任务取消成功");
            } else {
                return ApiResponse.error("任务取消失败，可能任务不存在或已完成");
            }
        } catch (Exception e) {
            log.error("取消任务失败: taskId={}", taskId, e);
            return ApiResponse.error("取消失败: " + e.getMessage());
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/tasks/{taskId}")
    @Operation(summary = "删除任务", description = "删除PCAP下载任务记录")
    public ApiResponse<String> deleteTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId,
            @Parameter(description = "用户ID", required = true)
            @RequestParam String userId) {
        try {
            boolean success = pcapDownloadService.deleteDownloadTask(taskId, userId);
            if (success) {
                return ApiResponse.success("任务删除成功");
            } else {
                return ApiResponse.error("任务删除失败，可能任务不存在");
            }
        } catch (Exception e) {
            log.error("删除任务失败: taskId={}", taskId, e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户任务列表
     */
    @GetMapping("/tasks")
    @Operation(summary = "获取任务列表", description = "分页查询用户的PCAP下载任务列表")
    public ApiResponse<List<PcapDownloadTaskResult>> getUserTasks(
            @Parameter(description = "用户ID", required = true)
            @RequestParam String userId,
            @Parameter(description = "页码")
            @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小")
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<PcapDownloadTaskResult> tasks = pcapDownloadService.getUserDownloadTasks(userId, page, size);
            return ApiResponse.success(tasks);
        } catch (Exception e) {
            log.error("查询用户任务列表失败: userId={}", userId, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量查询任务状态
     */
    @PostMapping("/tasks/batch-status")
    @Operation(summary = "批量查询任务状态", description = "批量查询多个任务的状态")
    public ApiResponse<List<PcapDownloadTaskResult>> getBatchTaskStatus(
            @Parameter(description = "任务ID列表", required = true)
            @RequestBody List<Integer> taskIds,
            @Parameter(description = "用户ID")
            @RequestParam(required = false) String userId) {
        try {
            List<PcapDownloadTaskResult> statusList = taskIds.stream()
                .map(pcapDownloadService::getDownloadTaskStatus)
                .toList();
            return ApiResponse.success(statusList);
        } catch (Exception e) {
            log.error("批量查询任务状态失败: taskIds={}", taskIds, e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }



    /**
     * 创建单会话下载任务（便民接口）
     */
    @PostMapping("/tasks/session/{sessionId}")
    @Operation(summary = "创建单会话下载任务", description = "为单个会话创建PCAP下载任务")
    public ApiResponse<PcapDownloadTaskResult> createSingleSessionTask(
            @Parameter(description = "会话ID", required = true)
            @PathVariable String sessionId,
            @Parameter(description = "用户ID", required = true)
            @RequestParam String userId,
            @Parameter(description = "下载名称")
            @RequestParam(required = false) String downloadName) {
        try {
            log.info("创建单会话PCAP下载任务: sessionId={}, userId={}", sessionId, userId);

            PcapDownloadRequest request = new PcapDownloadRequest(
                userId,
                "SINGLE_SESSION",
                System.currentTimeMillis(),
                List.of(sessionId),
                "单会话PCAP下载",
                downloadName != null ? downloadName : ("session_" + sessionId + ".pcap"),
                Map.of("sessionId", sessionId, "downloadType", "single")
            );

            PcapDownloadTaskResult result = pcapDownloadService.createPcapDownloadTask(request);
            return ApiResponse.success("单会话下载任务创建成功", result);

        } catch (Exception e) {
            log.error("创建单会话下载任务失败: sessionId={}", sessionId, e);
            return ApiResponse.error("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建批量会话下载任务（便民接口）
     */
    @PostMapping("/tasks/sessions/batch")
    @Operation(summary = "创建批量会话下载任务", description = "为多个会话创建PCAP下载任务")
    public ApiResponse<PcapDownloadTaskResult> createBatchSessionTask(
            @Parameter(description = "批量下载请求", required = true)
            @RequestBody BatchSessionDownloadRequest request) {
        try {
            log.info("创建批量会话PCAP下载任务: sessionCount={}, userId={}",
                    request.sessionIds().size(), request.userId());

            PcapDownloadRequest pcapRequest = new PcapDownloadRequest(
                request.userId(),
                "BATCH_SESSION",
                System.currentTimeMillis(),
                request.sessionIds(),
                "批量会话PCAP下载",
                request.downloadName(),
                Map.of("downloadType", "batch", "requestSource", request.requestSource())
            );

            PcapDownloadTaskResult result = pcapDownloadService.createPcapDownloadTask(pcapRequest);
            return ApiResponse.success("批量下载任务创建成功", result);

        } catch (Exception e) {
            log.error("创建批量下载任务失败", e);
            return ApiResponse.error("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 批量会话下载请求
     */
    public record BatchSessionDownloadRequest(
        List<String> sessionIds,
        String userId,
        String downloadName,
        String requestSource
    ) {}
}
