package com.geeksec.task.application.service;

import com.geeksec.services.common.shared.dto.ApiResponse;
import java.util.Map;
import com.geeksec.task.model.dto.TaskRegisterCondition;
import com.geeksec.task.model.entity.DownloadTaskRegister;

/**
 * 任务注册应用服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface TaskRegisterService {

    /**
     * 任务注册
     * 
     * @param condition 注册条件
     * @return 注册结果
     */
    ApiResponse<Map<String, Object>> taskRegister(TaskRegisterCondition condition);

    /**
     * 查询下载列表
     *
     * @param condition 查询条件
     * @return 下载列表
     */
    ApiResponse<String> searchList(TaskRegisterCondition condition);

    /**
     * 删除任务
     *
     * @param id 任务ID
     * @return 删除结果
     */
    ApiResponse<String> deleteTask(Integer id);

    /**
     * 下载数据
     * 
     * @param id 任务ID
     * @return 下载任务信息
     */
    DownloadTaskRegister downloadData(Integer id);

    /**
     * 数据准备作业（定时任务）
     */
    void dataPrepareJob();

    /**
     * 获取下一个待处理任务
     * 
     * @return 待处理任务
     */
    DownloadTaskRegister getNextTask();

    /**
     * 处理数据准备
     */
    void dataPrepare();
}
