package com.geeksec.task.application.service.impl;

import com.geeksec.common.utils.io.JsonUtils;
import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.task.application.service.PcapDownloadService;
import com.geeksec.task.domain.event.PcapDownloadTaskCreatedEvent;
import com.geeksec.task.infrastructure.client.SessionClient;
import com.geeksec.task.model.entity.PcapDownloadTask;
import com.geeksec.task.model.entity.Task;
import com.geeksec.task.repository.PcapDownloadTaskRepository;
import com.geeksec.task.repository.TaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP下载服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PcapDownloadServiceImpl implements PcapDownloadService {

    private final PcapDownloadTaskRepository pcapDownloadTaskRepository;
    private final TaskRepository taskRepository;
    private final SessionClient sessionClient;
    private final ApplicationEventPublisher eventPublisher;

    @Value("${app.task.pcap.expire-hours:24}")
    private int expireHours;

    /**
     * 最大文件大小限制 (1GB)
     */
    @Value("${app.task.pcap.max-file-size:1073741824}")
    private long maxFileSize;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PcapDownloadTaskResult createPcapDownloadTask(PcapDownloadRequest request) {
        log.info("创建PCAP下载任务: userId={}, sessionCount={}", 
                request.userId(), request.sessionIds().size());

        try {
            // 验证请求参数
            validateRequest(request);

            // 验证会话ID的有效性
            ApiResponse<SessionClient.SessionValidationResult> validationResponse =
                sessionClient.validateSessionIds(request.sessionIds());

            if (!validationResponse.isSuccess()) {
                throw new RuntimeException("会话验证失败: " + validationResponse.getMessage());
            }

            SessionClient.SessionValidationResult validation = validationResponse.getData();
            if (validation.validCount() == 0) {
                throw new RuntimeException("没有找到有效的会话ID");
            }

            if (validation.invalidCount() > 0) {
                log.warn("存在无效会话ID: 总数={}, 有效={}, 无效={}",
                        validation.totalCount(), validation.validCount(), validation.invalidCount());
            }

            // 创建主任务
            Task mainTask = createMainTask(request);
            taskRepository.save(mainTask);

            // 创建PCAP下载任务（使用有效的会话ID）
            PcapDownloadTask pcapTask = createPcapTask(request, mainTask.getTaskId(), validation.validSessionIds());
            pcapDownloadTaskRepository.insert(pcapTask);

            // 发布任务创建事件，触发异步处理
            publishTaskCreatedEvent(pcapTask, validation.validSessionIds());

            return convertToResult(pcapTask);
        } catch (Exception e) {
            log.error("创建PCAP下载任务失败", e);
            throw new RuntimeException("创建PCAP下载任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PcapDownloadTaskResult getDownloadTaskStatus(Integer taskId) {
        log.debug("查询下载任务状态: taskId={}", taskId);

        PcapDownloadTask task = pcapDownloadTaskRepository.selectOneById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        return convertToResult(task);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelDownloadTask(Integer taskId, String userId) {
        log.info("取消下载任务: taskId={}, userId={}", taskId, userId);

        PcapDownloadTask task = pcapDownloadTaskRepository.selectOneById(taskId);
        if (task == null) {
            return false;
        }

        if (!task.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此任务");
        }

        if (task.getStatus() == PcapDownloadTask.TaskStatus.COMPLETED.getCode()) {
            throw new RuntimeException("任务已完成，无法取消");
        }

        // 更新任务状态
        task.setStatus(PcapDownloadTask.TaskStatus.FAILED.getCode());
        task.setErrorMessage("用户取消");
        task.setUpdateTime(LocalDateTime.now());
        pcapDownloadTaskRepository.update(task);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDownloadTask(Integer taskId, String userId) {
        log.info("删除下载任务: taskId={}, userId={}", taskId, userId);

        PcapDownloadTask task = pcapDownloadTaskRepository.selectOneById(taskId);
        if (task == null) {
            return false;
        }

        if (!task.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此任务");
        }

        // 删除相关文件
        deleteTaskFiles(task);

        // 删除任务记录
        pcapDownloadTaskRepository.deleteById(taskId);

        return true;
    }

    @Override
    public List<PcapDownloadTaskResult> getUserDownloadTasks(String userId, int page, int size) {
        log.debug("查询用户下载任务: userId={}, page={}, size={}", userId, page, size);

        List<PcapDownloadTask> tasks = pcapDownloadTaskRepository.getUserTasks(userId, page, size);
        return tasks.stream()
                .map(this::convertToResult)
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupExpiredTasks() {
        log.info("清理过期的下载任务");

        LocalDateTime expireTime = LocalDateTime.now().minusHours(expireHours);
        List<PcapDownloadTask> expiredTasks = pcapDownloadTaskRepository.getExpiredTasks(expireTime);

        int cleanedCount = 0;
        for (PcapDownloadTask task : expiredTasks) {
            try {
                // 删除相关文件
                deleteTaskFiles(task);

                // 删除任务记录
                pcapDownloadTaskRepository.deleteById(task.getId());
                cleanedCount++;
            } catch (Exception e) {
                log.error("清理过期任务失败: taskId={}", task.getId(), e);
            }
        }

        log.info("清理过期任务完成: 清理数量={}", cleanedCount);
        return cleanedCount;
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(PcapDownloadRequest request) {
        if (request.userId() == null || request.userId().trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        if (request.sessionIds() == null || request.sessionIds().isEmpty()) {
            throw new IllegalArgumentException("会话ID列表不能为空");
        }

        // 限制单次下载会话数量
        final int maxSessionCount = 1000;
        if (request.sessionIds().size() > maxSessionCount) {
            throw new IllegalArgumentException("单次下载会话数量不能超过" + maxSessionCount + "个");
        }
    }

    /**
     * 创建主任务
     */
    private Task createMainTask(PcapDownloadRequest request) {
        Task task = new Task();
        task.setTaskName(request.downloadName() != null ? request.downloadName() : "PCAP文件下载");
        task.setTaskDescription(String.format("下载%s关联会话PCAP文件，会话数量: %d",
                request.requestSource(), request.sessionIds().size()));
        task.setTaskType("PCAP_DOWNLOAD");
        task.setTaskStatus("PENDING");
        // 转换为Integer类型
        task.setUserId(Integer.valueOf(request.userId()));
        task.setCreateTime(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        return task;
    }

    /**
     * 创建PCAP下载任务
     */
    private PcapDownloadTask createPcapTask(PcapDownloadRequest request, Integer taskId, List<String> validSessionIds) {
        PcapDownloadTask pcapTask = new PcapDownloadTask();
        pcapTask.setTaskId(taskId);
        pcapTask.setUserId(request.userId());
        pcapTask.setAlarmType(request.requestSource());
        pcapTask.setAlarmTime(request.alarmTime());
        // 使用验证后的会话ID
        pcapTask.setSessionIds(JsonUtils.toJsonString(validSessionIds));
        pcapTask.setTotalFileCount(validSessionIds.size());
        pcapTask.setProcessedFileCount(0);
        pcapTask.setStatus(PcapDownloadTask.TaskStatus.PENDING.getCode());
        pcapTask.setProgress(0);
        pcapTask.setCreateTime(LocalDateTime.now());
        pcapTask.setUpdateTime(LocalDateTime.now());
        pcapTask.setExpireTime(LocalDateTime.now().plusHours(expireHours));
        return pcapTask;
    }



    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Integer taskId, PcapDownloadTask.TaskStatus status, 
                                 Integer progress, String errorMessage) {
        PcapDownloadTask task = new PcapDownloadTask();
        task.setId(taskId);
        task.setStatus(status.getCode());
        task.setProgress(progress);
        task.setUpdateTime(LocalDateTime.now());

        if (status == PcapDownloadTask.TaskStatus.PROCESSING && task.getStartTime() == null) {
            task.setStartTime(LocalDateTime.now());
        }

        if (status == PcapDownloadTask.TaskStatus.COMPLETED) {
            task.setCompleteTime(LocalDateTime.now());
        }

        if (errorMessage != null) {
            task.setErrorMessage(errorMessage);
        }

        pcapDownloadTaskRepository.update(task);
    }

    /**
     * 转换为结果对象
     */
    private PcapDownloadTaskResult convertToResult(PcapDownloadTask task) {
        List<String> sessionIds = JsonUtils.parseArray(task.getSessionIds(), String.class);
        String status = PcapDownloadTask.TaskStatus.fromCode(task.getStatus()).name();

        return new PcapDownloadTaskResult(
            task.getId(),
            task.getUserId(),
            task.getAlarmType(),
            task.getAlarmTime(),
            sessionIds,
            status,
            task.getProgress(),
            task.getTotalFileCount(),
            task.getProcessedFileCount(),
            task.getArchiveFileSize(),
            task.getDownloadUrl(),
            task.getErrorMessage(),
            task.getCreateTime(),
            task.getStartTime(),
            task.getCompleteTime(),
            task.getExpireTime()
        );
    }

    /**
     * 发布任务创建事件
     */
    private void publishTaskCreatedEvent(PcapDownloadTask task, List<String> sessionIds) {
        try {
            PcapDownloadTaskCreatedEvent event = new PcapDownloadTaskCreatedEvent(this, task, sessionIds);
            eventPublisher.publishEvent(event);
            log.debug("发布任务创建事件: taskId={}", task.getId());
        } catch (Exception e) {
            log.error("发布任务创建事件失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 删除任务相关文件
     */
    private void deleteTaskFiles(PcapDownloadTask task) {
        try {
            if (task.getArchiveFilePath() != null && !task.getArchiveFilePath().isEmpty()) {
                java.nio.file.Path filePath = java.nio.file.Paths.get(task.getArchiveFilePath());
                if (java.nio.file.Files.exists(filePath)) {
                    java.nio.file.Files.delete(filePath);
                    log.debug("删除任务文件成功: taskId={}, filePath={}", task.getId(), task.getArchiveFilePath());
                }
            }
        } catch (Exception e) {
            log.warn("删除任务文件失败: taskId={}, filePath={}", task.getId(), task.getArchiveFilePath(), e);
        }
    }
}
