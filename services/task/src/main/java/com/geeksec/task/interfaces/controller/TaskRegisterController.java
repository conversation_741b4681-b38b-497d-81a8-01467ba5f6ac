package com.geeksec.task.interfaces.controller;

import com.geeksec.services.common.shared.dto.ApiResponse;
import java.util.Map;
import com.geeksec.task.application.service.TaskRegisterService;
import com.geeksec.task.model.dto.TaskRegisterCondition;
import com.geeksec.task.model.entity.DownloadTaskRegister;
import com.geeksec.services.common.shared.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 任务注册控制器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/task-register")
@RequiredArgsConstructor
@Validated
@Tag(name = "任务注册管理", description = "任务注册相关操作")
public class TaskRegisterController {

    private final TaskRegisterService taskRegisterService;

    /**
     * 任务注册
     */
    @PostMapping("/register")
    @Operation(summary = "任务注册", description = "注册新的下载任务")
    public ApiResponse<Map<String, Object>> taskRegister(
            @Parameter(description = "注册条件", required = true)
            @RequestBody @Valid TaskRegisterCondition condition) {
        try {
            return taskRegisterService.taskRegister(condition);
        } catch (Exception e) {
            log.error("任务注册失败", e);
            return ApiResponse.error("任务注册失败：" + e.getMessage());
        }
    }

    /**
     * 查询下载列表
     */
    @PostMapping("/search")
    @Operation(summary = "查询下载列表", description = "根据条件查询下载任务列表")
    public ApiResponse<String> searchList(
            @Parameter(description = "查询条件", required = true)
            @RequestBody @Valid TaskRegisterCondition condition) {
        try {
            return taskRegisterService.searchList(condition);
        } catch (Exception e) {
            log.error("查询下载列表失败", e);
            return ApiResponse.error("查询下载列表失败：" + e.getMessage());
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除任务", description = "删除指定的下载任务")
    public ApiResponse<String> deleteTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer id) {
        try {
            if (id == null || id <= 0) {
                return ApiResponse.error("任务ID不能为空或无效");
            }

            return taskRegisterService.deleteTask(id);
        } catch (Exception e) {
            log.error("删除任务失败，任务ID：{}", id, e);
            return ApiResponse.error("删除任务失败：" + e.getMessage());
        }
    }

    /**
     * 下载数据
     */
    @GetMapping("/{id}/download")
    @Operation(summary = "下载数据", description = "获取指定任务的下载数据")
    public ApiResponse<DownloadTaskRegister> downloadData(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer id) {
        try {
            if (id == null || id <= 0) {
                return ApiResponse.error("任务ID不能为空或无效");
            }

            DownloadTaskRegister result = taskRegisterService.downloadData(id);
            if (result == null) {
                return ApiResponse.error("任务不存在");
            }

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("下载数据失败，任务ID：{}", id, e);
            return ApiResponse.error("下载数据失败：" + e.getMessage());
        }
    }

    /**
     * 手动触发数据准备
     */
    @PostMapping("/prepare")
    @Operation(summary = "手动触发数据准备", description = "手动触发数据准备任务")
    public ApiResponse<Void> triggerDataPrepare() {
        try {
            taskRegisterService.dataPrepare();
            return ApiResponse.<Void>success("数据准备任务已触发", null);
        } catch (Exception e) {
            log.error("触发数据准备失败", e);
            return ApiResponse.error("触发数据准备失败：" + e.getMessage());
        }
    }

    /**
     * 获取下一个待处理任务
     */
    @GetMapping("/next")
    @Operation(summary = "获取下一个待处理任务", description = "获取队列中下一个待处理的任务")
    public ApiResponse<DownloadTaskRegister> getNextTask() {
        try {
            DownloadTaskRegister nextTask = taskRegisterService.getNextTask();
            return ApiResponse.success(nextTask);
        } catch (Exception e) {
            log.error("获取下一个待处理任务失败", e);
            return ApiResponse.error("获取下一个待处理任务失败：" + e.getMessage());
        }
    }
}
