package com.geeksec.task.infrastructure.client;

import com.geeksec.services.common.shared.dto.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Session服务Feign客户端
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@FeignClient(name = "session-service", path = "/api/v1/session")
public interface SessionClient {

    /**
     * 根据会话ID列表查询PCAP文件信息
     * 
     * @param sessionIds 会话ID列表
     * @return PCAP文件信息列表
     */
    @PostMapping("/pcap/files")
    ApiResponse<List<SessionPcapInfo>> getSessionPcapFiles(@RequestBody List<String> sessionIds);

    /**
     * 验证会话ID列表的有效性
     * 
     * @param sessionIds 会话ID列表
     * @return 验证结果
     */
    @PostMapping("/pcap/validate")
    ApiResponse<SessionValidationResult> validateSessionIds(@RequestBody List<String> sessionIds);

    /**
     * 会话PCAP文件信息
     */
    record SessionPcapInfo(
        String sessionId,
        String pcapFilePath,
        Long fileSize,
        LocalDateTime sessionStartTime,
        LocalDateTime sessionEndTime,
        String srcIp,
        String dstIp,
        Integer srcPort,
        Integer dstPort,
        Integer protocol
    ) {}

    /**
     * 会话验证结果
     */
    record SessionValidationResult(
        List<String> validSessionIds,
        List<String> invalidSessionIds,
        int totalCount,
        int validCount,
        int invalidCount
    ) {}
}
