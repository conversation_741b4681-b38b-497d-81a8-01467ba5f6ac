package com.geeksec.admin.application.service.user;

import com.geeksec.admin.domain.model.user.UserDisplayPreference;

import java.util.List;
import java.util.Map;

/**
 * 用户显示偏好配置服务接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public interface UserDisplayPreferenceService {

    /**
     * 获取用户的显示配置
     * 
     * @param userId 用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @return 配置信息，包含success标志和data数据
     */
    Map<String, Object> getUserDisplayConfig(Integer userId, String module, String configType);

    /**
     * 保存用户的显示配置
     * 
     * @param userId 用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @param configJson 配置JSON
     * @param operatorId 操作者用户ID
     * @return 保存结果
     */
    boolean saveUserDisplayConfig(Integer userId, String module, String configType, String configJson, Integer operatorId);

    /**
     * 更新用户的显示配置
     * 
     * @param userId 用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @param configJson 配置JSON
     * @param operatorId 操作者用户ID
     * @return 更新结果
     */
    boolean updateUserDisplayConfig(Integer userId, String module, String configType, String configJson, Integer operatorId);

    /**
     * 删除用户的显示配置
     * 
     * @param userId 用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @return 删除结果
     */
    boolean deleteUserDisplayConfig(Integer userId, String module, String configType);

    /**
     * 获取用户的所有显示配置
     * 
     * @param userId 用户ID
     * @return 配置列表
     */
    List<UserDisplayPreference> getUserAllDisplayConfigs(Integer userId);

    /**
     * 获取用户在特定模块的所有配置
     * 
     * @param userId 用户ID
     * @param module 模块名称
     * @return 配置列表
     */
    List<UserDisplayPreference> getUserModuleDisplayConfigs(Integer userId, String module);

    /**
     * 检查用户是否有特定配置
     * 
     * @param userId 用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @return 是否有配置
     */
    boolean hasUserDisplayConfig(Integer userId, String module, String configType);

    /**
     * 获取默认配置
     * 
     * @param module 模块名称
     * @param configType 配置类型
     * @return 默认配置JSON
     */
    String getDefaultConfig(String module, String configType);

    /**
     * 复制配置
     * 
     * @param sourceUserId 源用户ID
     * @param targetUserId 目标用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @param operatorId 操作者用户ID
     * @return 复制结果
     */
    boolean copyDisplayConfig(Integer sourceUserId, Integer targetUserId, String module, String configType, Integer operatorId);

    /**
     * 批量删除用户配置
     * 
     * @param userId 用户ID
     * @param module 模块名称（可选，为null时删除用户所有配置）
     * @return 删除结果
     */
    boolean batchDeleteUserDisplayConfigs(Integer userId, String module);

    /**
     * 获取模块的所有配置统计
     * 
     * @param module 模块名称
     * @return 统计信息
     */
    Map<String, Object> getModuleConfigStatistics(String module);
}
