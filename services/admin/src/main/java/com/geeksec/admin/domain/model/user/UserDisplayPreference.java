package com.geeksec.admin.domain.model.user;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户显示偏好配置实体类
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("user_display_preferences")
public class UserDisplayPreference {

    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Integer id;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Integer userId;

    /**
     * 模块名称
     * 如：certificate, session, alert, traffic 等
     */
    @Column("module")
    private String module;

    /**
     * 配置类型
     * 如：table_view, chart_view, filter_view 等
     */
    @Column("config_type")
    private String configType;

    /**
     * 配置内容JSON
     * 包含列配置、排序、分页等信息
     */
    @Column("config_json")
    private String configJson;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建者用户ID
     */
    @Column("created_by")
    private Integer createdBy;

    /**
     * 更新者用户ID
     */
    @Column("updated_by")
    private Integer updatedBy;

    /**
     * 模块常量定义
     */
    public static class Module {
        public static final String CERTIFICATE = "certificate";
        public static final String SESSION = "session";
        public static final String ALERT = "alert";
        public static final String TRAFFIC = "traffic";
        public static final String GRAPH = "graph";
    }

    /**
     * 配置类型常量定义
     */
    public static class ConfigType {
        public static final String TABLE_VIEW = "table_view";
        public static final String CHART_VIEW = "chart_view";
        public static final String FILTER_VIEW = "filter_view";
        public static final String DASHBOARD_VIEW = "dashboard_view";
    }
}
