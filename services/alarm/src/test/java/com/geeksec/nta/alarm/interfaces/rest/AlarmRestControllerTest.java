package com.geeksec.nta.alarm.interfaces.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.services.common.shared.dto.PageResultVo;
import com.geeksec.nta.alarm.application.service.AlarmQueryService;
import com.geeksec.nta.alarm.application.service.AlarmCommandService;
import com.geeksec.nta.alarm.application.integration.AlarmStatisticsService;
import com.geeksec.nta.alarm.application.integration.AlarmExportService;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmDetailResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmListResponse;
import com.geeksec.nta.alarm.interfaces.exception.GlobalExceptionHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 告警REST控制器测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@WebMvcTest(AlarmRestController.class)
@Import(GlobalExceptionHandler.class)
@DisplayName("告警REST控制器测试")
class AlarmRestControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @MockBean
    private AlarmQueryService alarmQueryService;
    
    @MockBean
    private AlarmCommandService alarmCommandService;
    
    @MockBean
    private AlarmStatisticsService alarmStatisticsService;
    
    @MockBean
    private AlarmExportService alarmExportService;
    
    private AlarmId testAlarmId;
    private AlarmListResponse testAlarmListResponse;
    private AlarmDetailResponse testAlarmDetailResponse;
    
    @BeforeEach
    void setUp() {
        testAlarmId = new AlarmId("test-alarm-001");
        
        testAlarmListResponse = AlarmListResponse.builder()
                .id(testAlarmId.value())
                .alarmType("TEST_ALARM")
                .severity("HIGH")
                .status("PENDING")
                .build();
        
        testAlarmDetailResponse = AlarmDetailResponse.builder()
                .id(testAlarmId.value())
                .alarmType("TEST_ALARM")
                .severity("HIGH")
                .status("PENDING")
                .description("测试告警详情")
                .build();
    }
    
    @Test
    @DisplayName("查询告警列表 - 成功")
    void getAlarms_Success() throws Exception {
        // Given
        PageResultVo<AlarmListResponse> pageResult = PageResultVo.<AlarmListResponse>builder()
                .records(List.of(testAlarmListResponse))
                .total(1L)
                .current(1)
                .size(20)
                .build();
        
        when(alarmQueryService.queryAlarmList(any())).thenReturn(pageResult);
        
        // When & Then
        mockMvc.perform(get("/api/v1/alarms")
                        .param("page", "1")
                        .param("size", "20")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.records").isArray())
                .andExpect(jsonPath("$.data.records[0].id").value(testAlarmId.value()))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.message").value("查询成功"));
        
        verify(alarmQueryService).queryAlarmList(any());
    }
    
    @Test
    @DisplayName("查询告警详情 - 成功")
    void getAlarm_Success() throws Exception {
        // Given
        when(alarmQueryService.queryAlarmById(testAlarmId)).thenReturn(testAlarmDetailResponse);
        
        // When & Then
        mockMvc.perform(get("/api/v1/alarms/{id}", testAlarmId.value())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(testAlarmId.value()))
                .andExpect(jsonPath("$.data.alarmType").value("TEST_ALARM"))
                .andExpect(jsonPath("$.message").value("查询成功"));
        
        verify(alarmQueryService).queryAlarmById(testAlarmId);
    }
    
    @Test
    @DisplayName("查询告警详情 - 告警不存在")
    void getAlarm_NotFound() throws Exception {
        // Given
        when(alarmQueryService.queryAlarmById(testAlarmId))
                .thenThrow(new RuntimeException("告警不存在: " + testAlarmId.value()));
        
        // When & Then
        mockMvc.perform(get("/api/v1/alarms/{id}", testAlarmId.value())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error.code").value("SYSTEM_ERROR"));
        
        verify(alarmQueryService).queryAlarmById(testAlarmId);
    }
    
    @Test
    @DisplayName("检查告警是否存在 - 存在")
    void existsAlarm_Exists() throws Exception {
        // Given
        when(alarmQueryService.existsById(testAlarmId)).thenReturn(true);
        
        // When & Then
        mockMvc.perform(get("/api/v1/alarms/{id}/exists", testAlarmId.value())
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
        
        verify(alarmQueryService).existsById(testAlarmId);
    }
    
    @Test
    @DisplayName("更新告警状态 - 成功")
    void updateAlarmStatus_Success() throws Exception {
        // Given
        when(alarmCommandService.updateAlarmStatus(any())).thenReturn(true);
        
        String requestBody = """
                {
                    "newStatus": "RESOLVED",
                    "operator": "test-user",
                    "reason": "问题已解决"
                }
                """;
        
        // When & Then
        mockMvc.perform(patch("/api/v1/alarms/{id}/status", testAlarmId.value())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("状态更新成功"));
        
        verify(alarmCommandService).updateAlarmStatus(any());
    }
    
    @Test
    @DisplayName("更新告警状态 - 参数验证失败")
    void updateAlarmStatus_ValidationFailed() throws Exception {
        // Given
        String invalidRequestBody = """
                {
                    "newStatus": "",
                    "operator": "",
                    "reason": ""
                }
                """;
        
        // When & Then
        mockMvc.perform(patch("/api/v1/alarms/{id}/status", testAlarmId.value())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(invalidRequestBody))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error.code").value("VALIDATION_ERROR"));
        
        verify(alarmCommandService, never()).updateAlarmStatus(any());
    }
    
    @Test
    @DisplayName("删除告警 - 成功")
    void deleteAlarm_Success() throws Exception {
        // Given
        when(alarmCommandService.deleteAlarm(any())).thenReturn(true);
        
        // When & Then
        mockMvc.perform(delete("/api/v1/alarms/{id}", testAlarmId.value())
                        .param("operator", "test-user")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("删除成功"));
        
        verify(alarmCommandService).deleteAlarm(any());
    }
    
    @Test
    @DisplayName("批量删除告警 - 成功")
    void batchDeleteAlarms_Success() throws Exception {
        // Given
        when(alarmCommandService.batchDeleteAlarms(any())).thenReturn(2);
        
        // When & Then
        mockMvc.perform(delete("/api/v1/alarms")
                        .param("ids", "alarm-001", "alarm-002")
                        .param("operator", "test-user")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(2))
                .andExpect(jsonPath("$.message").value("成功删除2个告警"));
        
        verify(alarmCommandService).batchDeleteAlarms(any());
    }
    
    @Test
    @DisplayName("查询告警列表 - 参数验证失败")
    void getAlarms_InvalidParameters() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/alarms")
                        .param("page", "0") // 无效页码
                        .param("size", "2000") // 超出限制
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error.code").value("VALIDATION_ERROR"));
        
        verify(alarmQueryService, never()).queryAlarmList(any());
    }
}
