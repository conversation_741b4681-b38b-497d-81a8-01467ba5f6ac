package com.geeksec.nta.alarm.interfaces.rest;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.services.common.shared.dto.ApiResponse;

import com.geeksec.nta.alarm.application.service.AlarmSuppressionCommandService;
import com.geeksec.nta.alarm.application.integration.AlarmSuppressionProviderService;
import com.geeksec.nta.alarm.application.service.AlarmSuppressionQueryService;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.nta.alarm.interfaces.dto.request.CreateSuppressionRequest;

import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSuppressionResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警抑制REST控制器
 * 负责告警抑制规则资源的RESTful API
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/suppressions")
@RequiredArgsConstructor
@Tag(name = "告警抑制", description = "告警抑制规则管理相关接口")
public class AlarmSuppressionRestController {
    
    private final AlarmSuppressionQueryService suppressionQueryService;
    private final AlarmSuppressionCommandService suppressionCommandService;
    private final AlarmSuppressionProviderService suppressionProviderService;
    
    // ==================== 抑制规则查询操作 ====================
    
    /**
     * 查询抑制规则列表
     */
    @GetMapping
    @Operation(summary = "查询抑制规则列表", description = "查询所有告警抑制规则列表")
    public ApiResponse<List<AlarmSuppressionResponse>> getSuppressions() {

        log.info("查询抑制规则列表");

        var suppressions = suppressionQueryService.getAllSuppressions();

        return ApiResponse.success("查询成功", suppressions);
    }
    
    /**
     * 查询抑制规则详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询抑制规则详情", description = "根据抑制规则ID查询详细信息")
    public ApiResponse<AlarmSuppressionResponse> getSuppression(
            @Parameter(description = "抑制规则ID") @PathVariable String id) {
        
        log.info("查询抑制规则详情: {}", id);
        
        var suppressionId = SuppressionId.of(id);
        var suppression = suppressionQueryService.getSuppression(suppressionId);
        
        if (suppression.isPresent()) {
            return ApiResponse.success("查询成功", suppression.get());
        } else {
            return ApiResponse.notFound("抑制规则");
        }
    }
    

    
    // ==================== 抑制规则管理操作 ====================
    
    /**
     * 创建抑制规则
     */
    @PostMapping
    @Operation(summary = "创建抑制规则", description = "创建新的告警抑制规则")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<String> createSuppression(
            @Valid @RequestBody CreateSuppressionRequest request) {
        
        log.info("创建抑制规则: {}", request);
        
        var command = request.toCommand();
        var suppressionId = suppressionCommandService.createSuppression(command);
        
        return ApiResponse.success("抑制规则创建成功", suppressionId.toString());
    }
    
    /**
     * 删除抑制规则
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除抑制规则", description = "删除指定的抑制规则")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ApiResponse<String> deleteSuppression(
            @Parameter(description = "抑制规则ID") @PathVariable String id,
            @Parameter(description = "操作人") @RequestParam String operator) {
        
        log.info("删除抑制规则: id={}, operator={}", id, operator);
        
        var command = com.geeksec.nta.alarm.application.command.DeleteSuppressionCommand.builder()
                .suppressionId(com.geeksec.nta.alarm.domain.valueobject.SuppressionId.of(id))
                .build();
        
        boolean success = suppressionCommandService.deleteSuppression(command);
        
        if (success) {
            return ApiResponse.success("抑制规则删除成功");
        } else {
            return ApiResponse.businessError("抑制规则删除失败");
        }
    }
    

    
    // ==================== 抑制规则提供操作（供flink-jobs调用） ====================

    /**
     * 获取抑制规则
     * 供flink-jobs/alarm-processor调用
     */
    @GetMapping("/rules")
    @Operation(summary = "获取抑制规则", description = "获取抑制规则，供flink-jobs调用")
    public ApiResponse<List<AlarmSuppressionProviderService.SuppressionRuleDto>> getRules() {

        log.info("获取抑制规则（供flink-jobs调用）");

        var rules = suppressionProviderService.getAllSuppressionRules();

        return ApiResponse.success("查询成功", rules);
    }


    

}
