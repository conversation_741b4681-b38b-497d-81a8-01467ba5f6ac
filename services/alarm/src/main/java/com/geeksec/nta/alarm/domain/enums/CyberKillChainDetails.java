package com.geeksec.nta.alarm.domain.enums;

import com.geeksec.nta.alarm.domain.enums.CyberKillChain;
import com.mybatisflex.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * Cyber Kill Chain详情实体类
 * 对应 cyber_kill_chain_details 表
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("cyber_kill_chain_details")
public class CyberKillChainDetails implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Cyber Kill Chain阶段枚举值（主键）
     */
    @Id(keyType = KeyType.None)
    @Column("cyber_kill_chain")
    private CyberKillChain cyberKillChain;

    /**
     * 阶段中文名称
     */
    @Column("chinese_name")
    private String chineseName;

    /**
     * 阶段英文名称
     */
    @Column("english_name")
    private String englishName;

    /**
     * 攻击阶段详细描述
     */
    @Column("description")
    private String description;

    /**
     * 典型攻击技术列表
     */
    @Column("typical_techniques")
    private String[] typicalTechniques;

    /**
     * 严重程度等级(1-10，数字越大越严重)
     */
    @Column("severity_level")
    private Integer severityLevel;

    /**
     * 紧急程度描述
     */
    @Column("urgency_description")
    private String urgencyDescription;

    /**
     * 防护建议列表
     */
    @Column("defense_recommendations")
    private String[] defenseRecommendations;
}
