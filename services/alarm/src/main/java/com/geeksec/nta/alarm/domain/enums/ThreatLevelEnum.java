package com.geeksec.nta.alarm.domain.enums;

import lombok.Getter;

/**
 * 威胁等级枚举 (0-4级)
 * 统一的威胁等级定义，适用于所有服务模块
 *
 * <AUTHOR>
 */
@Getter
public enum ThreatLevelEnum {

    /** 无危 - 无威胁 */
    NONE(0, "无危", "无威胁或安全状态"),
    /** 低危 - 低威胁 */
    LOW(1, "低危", "对系统影响较小的威胁"),
    /** 中危 - 中等威胁 */
    MEDIUM(2, "中危", "对系统有一定影响的威胁"),
    /** 高危 - 高威胁 */
    HIGH(3, "高危", "对系统有严重影响的威胁"),
    /** 严重 - 严重威胁 */
    CRITICAL(4, "严重", "对系统有极其严重影响的威胁");

    private final int code;
    private final String displayName;
    private final String description;

    ThreatLevelEnum(int code, String displayName, String description) {
        this.code = code;
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 根据代码查找枚举
     *
     * @param code 威胁等级代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static ThreatLevelEnum findByCode(int code) {
        for (ThreatLevelEnum levelEnum : values()) {
            if (levelEnum.code == code) {
                return levelEnum;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 威胁等级代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        return findByCode(code) != null;
    }

    /**
     * 根据名称查找枚举
     *
     * @param name 威胁等级名称
     * @return 对应的枚举，如果未找到返回null
     */
    public static ThreatLevelEnum findByName(String name) {
        try {
            return valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 根据显示名称查找枚举
     *
     * @param displayName 威胁等级显示名称
     * @return 对应的枚举，如果未找到返回NONE
     */
    public static ThreatLevelEnum findByDisplayName(String displayName) {
        if (displayName == null) {
            return NONE;
        }
        for (ThreatLevelEnum levelEnum : values()) {
            if (levelEnum.displayName.equals(displayName)) {
                return levelEnum;
            }
        }
        return NONE;
    }

    /**
     * 根据等级数值获取威胁等级
     *
     * @param level 等级数值
     * @return 对应的枚举，如果未找到返回NONE
     */
    public static ThreatLevelEnum fromLevel(int level) {
        return findByCode(level) != null ? findByCode(level) : NONE;
    }
}
