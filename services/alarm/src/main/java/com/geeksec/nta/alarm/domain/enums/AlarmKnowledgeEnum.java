package com.geeksec.nta.alarm.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description：告警标签知识库枚举
 */
@Getter
public enum AlarmKnowledgeEnum {

    SCAN_BEHAVIOR(100002,"扫描行为"),
    REMOTE_TROJAN(100005,"远程木马"),
    MINING_VIRUS(100006,"挖矿病毒"),
    ILLEGAL_EXTRA_CONN(100009,"违规外联"),
    CONVERT_TUNNEL(100010,"隐蔽隧道"),
    MINING_CONN(100017,"尝试挖矿连接"),
    SSLFINGER_RANDOM(12004,"指纹随机化访问服务端");

    private Integer alarmKnowledgeId;

    private String alarmKnowledgeName;

    AlarmKnowledgeEnum(Integer code, String name) {
        this.alarmKnowledgeId = code;
        this.alarmKnowledgeName = name;
    }



}
