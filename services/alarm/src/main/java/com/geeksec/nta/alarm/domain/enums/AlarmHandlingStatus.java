package com.geeksec.nta.alarm.domain.enums;

import lombok.Getter;

/**
 * 告警处理状态枚举
 * 定义告警在处理流程中的状态：未处理 → 已确认 → 已处理
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum AlarmHandlingStatus {

    /**
     * 未处理 - 告警刚产生，尚未被处理
     */
    UNHANDLED(0, "未处理"),

    /**
     * 未确认 - 告警刚产生，尚未被确认
     */
    UNCONFIRMED(1, "未确认"),

    /**
     * 已确认 - 告警已被确认但尚未处理完成
     */
    CONFIRMED(2, "已确认"),

    /**
     * 已处理 - 告警已完成处理
     */
    HANDLED(3, "已处理"),

    /**
     * 误报 - 告警被标记为误报
     */
    FALSE_POSITIVE(4, "误报");
    
    private final int code;
    private final String description;
    
    AlarmHandlingStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }
    

    
    /**
     * 根据代码获取告警处理状态
     *
     * @param code 状态代码
     * @return 告警处理状态
     */
    public static AlarmHandlingStatus fromCode(int code) {
        for (AlarmHandlingStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的告警处理状态代码: " + code);
    }

    /**
     * 检查是否为已处理状态
     *
     * @return 是否已处理
     */
    public boolean isHandled() {
        return this == HANDLED;
    }

    /**
     * 检查是否为未处理状态
     *
     * @return 是否未处理
     */
    public boolean isUnhandled() {
        return this == UNHANDLED;
    }

    /**
     * 检查是否为未确认状态
     *
     * @return 是否未确认
     */
    public boolean isUnconfirmed() {
        return this == UNCONFIRMED;
    }

    /**
     * 检查是否为已确认状态
     *
     * @return 是否已确认
     */
    public boolean isConfirmed() {
        return this == CONFIRMED;
    }

    /**
     * 检查是否为误报状态
     *
     * @return 是否为误报
     */
    public boolean isFalsePositive() {
        return this == FALSE_POSITIVE;
    }

    /**
     * 检查是否可以进行下一步处理
     *
     * @return 是否可以进行下一步处理
     */
    public boolean canProceedToNext() {
        return this != HANDLED && this != FALSE_POSITIVE;
    }

    /**
     * 获取下一个状态
     *
     * @return 下一个状态，如果已是最终状态则返回当前状态
     */
    public AlarmHandlingStatus getNextStatus() {
        return switch (this) {
            case UNHANDLED -> UNCONFIRMED;
            case UNCONFIRMED -> CONFIRMED;
            case CONFIRMED -> HANDLED;
            // 已处理状态和误报状态不再变化
            case HANDLED, FALSE_POSITIVE -> this;
        };
    }

    /**
     * 检查是否可以从当前状态转换到目标状态
     *
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(AlarmHandlingStatus targetStatus) {
        if (targetStatus == null) {
            return false;
        }

        return switch (this) {
            case UNHANDLED -> targetStatus == UNCONFIRMED || targetStatus == CONFIRMED ||
                             targetStatus == HANDLED || targetStatus == FALSE_POSITIVE;
            case UNCONFIRMED -> targetStatus == CONFIRMED || targetStatus == HANDLED ||
                               targetStatus == FALSE_POSITIVE;
            case CONFIRMED -> targetStatus == HANDLED || targetStatus == FALSE_POSITIVE;
            // 已处理状态和误报状态不能再转换
            case HANDLED, FALSE_POSITIVE -> false;
        };
    }
}
