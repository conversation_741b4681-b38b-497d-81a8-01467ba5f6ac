package com.geeksec.nta.alarm.interfaces.rest;

import com.geeksec.services.common.shared.dto.PageResultVo;
import com.geeksec.nta.alarm.application.service.NotificationResultQueryService;
import com.geeksec.nta.alarm.domain.aggregate.notification.NotificationResult;
import com.geeksec.nta.alarm.infrastructure.mapper.NotificationResultMapper;
import com.mybatisflex.core.paginate.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 通知结果管理控制器
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/notification-results")
@RequiredArgsConstructor
@Tag(name = "通知结果管理", description = "通知结果查询和统计相关接口")
public class NotificationResultController {

    private final NotificationResultQueryService notificationResultQueryService;

    /**
     * 根据ID查询通知结果
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询通知结果", description = "根据通知结果ID查询详细信息")
    public NotificationResult getById(
            @Parameter(description = "通知结果ID") @PathVariable String id) {
        log.info("根据ID查询通知结果: id={}", id);

        Optional<NotificationResult> result = notificationResultQueryService.findById(id);
        return result.orElse(null);
    }

    /**
     * 分页查询通知结果
     */
    @GetMapping
    @Operation(summary = "分页查询通知结果", description = "根据条件分页查询通知结果")
    public PageResultVo<NotificationResult> queryByPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize,
            @Parameter(description = "订阅ID") @RequestParam(required = false) String subscriptionId,
            @Parameter(description = "告警ID") @RequestParam(required = false) String alarmId,
            @Parameter(description = "通知渠道") @RequestParam(required = false) String channel,
            @Parameter(description = "发送状态") @RequestParam(required = false) String status,
            @Parameter(description = "开始时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        log.info("分页查询通知结果: pageNum={}, pageSize={}, subscriptionId={}, alarmId={}",
                pageNum, pageSize, subscriptionId, alarmId);

        try {
            Page<NotificationResult> page = notificationResultQueryService.findByPage(
                    pageNum, pageSize, subscriptionId, alarmId, channel, status, startTime, endTime);

            PageResultVo<NotificationResult> result = PageResultVo.of(
                    page.getRecords(),
                    page.getTotalRow(),
                    page.getPageNumber(),
                    page.getPageSize()
            );

            log.info("分页查询通知结果成功: total={}", page.getTotalRow());
            return result;
        } catch (Exception e) {
            log.error("分页查询通知结果失败", e);
            throw new RuntimeException("分页查询通知结果失败", e);
        }
    }

    /**
     * 根据告警ID查询通知结果
     */
    @GetMapping("/alarm/{alarmId}")
    @Operation(summary = "根据告警ID查询通知结果", description = "查询指定告警的所有通知结果")
    public List<NotificationResult> getByAlarmId(
            @Parameter(description = "告警ID") @PathVariable String alarmId) {
        log.info("根据告警ID查询通知结果: alarmId={}", alarmId);

        try {
            List<NotificationResult> results = notificationResultQueryService.findByAlarmId(alarmId);
            log.info("根据告警ID查询通知结果成功: alarmId={}, count={}", alarmId, results.size());
            return results;
        } catch (Exception e) {
            log.error("根据告警ID查询通知结果失败: alarmId={}", alarmId, e);
            throw new RuntimeException("根据告警ID查询通知结果失败", e);
        }
    }

    /**
     * 根据订阅ID查询通知结果
     */
    @GetMapping("/subscription/{subscriptionId}")
    @Operation(summary = "根据订阅ID查询通知结果", description = "查询指定订阅的通知结果")
    public List<NotificationResult> getBySubscriptionId(
            @Parameter(description = "订阅ID") @PathVariable String subscriptionId,
            @Parameter(description = "查询数量限制") @RequestParam(required = false) Integer limit) {
        log.info("根据订阅ID查询通知结果: subscriptionId={}, limit={}", subscriptionId, limit);

        try {
            List<NotificationResult> results = notificationResultQueryService.findBySubscriptionId(subscriptionId, limit);
            log.info("根据订阅ID查询通知结果成功: subscriptionId={}, count={}", subscriptionId, results.size());
            return results;
        } catch (Exception e) {
            log.error("根据订阅ID查询通知结果失败: subscriptionId={}", subscriptionId, e);
            throw new RuntimeException("根据订阅ID查询通知结果失败", e);
        }
    }

    /**
     * 查询失败的通知记录
     */
    @GetMapping("/failed")
    @Operation(summary = "查询失败的通知记录", description = "查询发送失败的通知记录")
    public List<NotificationResult> getFailedNotifications(
            @Parameter(description = "最大重试次数") @RequestParam(required = false) Integer maxRetryCount,
            @Parameter(description = "查询数量限制") @RequestParam(required = false) Integer limit) {
        log.info("查询失败的通知记录: maxRetryCount={}, limit={}", maxRetryCount, limit);

        try {
            List<NotificationResult> results = notificationResultQueryService.findFailedNotifications(maxRetryCount, limit);
            log.info("查询失败的通知记录成功: count={}", results.size());
            return results;
        } catch (Exception e) {
            log.error("查询失败的通知记录失败", e);
            throw new RuntimeException("查询失败的通知记录失败", e);
        }
    }

    /**
     * 查询通知统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "查询通知统计信息", description = "查询指定时间范围内的通知统计信息")
    public NotificationResultMapper.NotificationStatistics getStatistics(
            @Parameter(description = "订阅ID") @RequestParam(required = false) String subscriptionId,
            @Parameter(description = "开始时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        log.info("查询通知统计信息: subscriptionId={}", subscriptionId);

        try {
            NotificationResultMapper.NotificationStatistics statistics =
                    notificationResultQueryService.getStatistics(subscriptionId, startTime, endTime);
            log.info("查询通知统计信息成功: subscriptionId={}", subscriptionId);
            return statistics;
        } catch (Exception e) {
            log.error("查询通知统计信息失败: subscriptionId={}", subscriptionId, e);
            throw new RuntimeException("查询通知统计信息失败", e);
        }
    }

    /**
     * 查询每日成功率
     */
    @GetMapping("/daily-success-rate")
    @Operation(summary = "查询每日成功率", description = "查询指定天数内的每日通知成功率")
    public List<NotificationResultMapper.DailySuccessRate> getDailySuccessRate(
            @Parameter(description = "订阅ID") @RequestParam(required = false) String subscriptionId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") Integer days) {
        log.info("查询每日成功率: subscriptionId={}, days={}", subscriptionId, days);

        try {
            List<NotificationResultMapper.DailySuccessRate> rates =
                    notificationResultQueryService.getDailySuccessRate(subscriptionId, days);
            log.info("查询每日成功率成功: subscriptionId={}, days={}", subscriptionId, days);
            return rates;
        } catch (Exception e) {
            log.error("查询每日成功率失败: subscriptionId={}, days={}", subscriptionId, days, e);
            throw new RuntimeException("查询每日成功率失败", e);
        }
    }
}
