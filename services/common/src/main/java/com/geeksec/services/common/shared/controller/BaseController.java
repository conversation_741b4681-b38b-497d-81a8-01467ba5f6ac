package com.geeksec.services.common.shared.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 基础控制器类
 * 
 * 提供控制器层的通用功能和工具方法
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
public abstract class BaseController {
    
    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    protected Long getCurrentUserId() {
        // TODO: 从安全上下文中获取当前用户ID
        return 1L;
    }
    
    /**
     * 获取当前用户名
     * 
     * @return 用户名
     */
    protected String getCurrentUsername() {
        // TODO: 从安全上下文中获取当前用户名
        return "admin";
    }
    
    /**
     * 记录操作日志
     * 
     * @param operation 操作描述
     * @param result 操作结果
     */
    protected void logOperation(String operation, Object result) {
        log.info("用户操作: {}, 结果: {}", operation, result);
    }
}
