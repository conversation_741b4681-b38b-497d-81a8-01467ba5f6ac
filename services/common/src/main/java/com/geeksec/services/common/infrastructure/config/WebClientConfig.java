package com.geeksec.services.common.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * WebClient配置类，用于替代OpenFeign进行服务间调用
 * 
 * <AUTHOR>
 */
@Configuration
public class WebClientConfig {
    
    /**
     * 创建WebClient.Builder Bean
     * 
     * @return WebClient.Builder实例
     */
    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }
}
