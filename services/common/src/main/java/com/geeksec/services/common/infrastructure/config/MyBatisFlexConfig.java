package com.geeksec.services.common.infrastructure.config;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.ConsoleMessageCollector;
import com.mybatisflex.core.audit.MessageCollector;
import com.mybatisflex.core.mybatis.FlexConfiguration;
import com.mybatisflex.spring.boot.ConfigurationCustomizer;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Flex 统一配置类
 *
 * 统一配置 MyBatis-Flex ORM 框架的相关设置：
 * - 全局配置（逻辑删除、乐观锁）
 * - 审计功能
 * - SQL 收集器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Configuration
public class MyBatisFlexConfig implements ConfigurationCustomizer, MyBatisFlexCustomizer {

    @Override
    public void customize(FlexConfiguration configuration) {
        // 开启审计功能
        AuditManager.setAuditEnable(true);

        // 设置 SQL 审计收集器
        MessageCollector collector = new ConsoleMessageCollector();
        AuditManager.setMessageCollector(collector);

        log.info("MyBatis-Flex 配置初始化完成");
    }

    @Override
    public void customize(FlexGlobalConfig globalConfig) {
        // 设置逻辑删除字段
        globalConfig.setLogicDeleteColumn("deleted");

        // 设置乐观锁版本字段
        globalConfig.setVersionColumn("version");

        log.info("MyBatis-Flex 全局配置初始化完成");
    }
}
