package com.geeksec.services.common.shared.utils;

import com.geeksec.common.utils.ValidationUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP 请求工具类
 *
 * 提供 HTTP 请求处理相关的工具方法：
 * - 请求参数转换
 * - 参数验证
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public final class HttpRequestUtils {

    private HttpRequestUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 请求参数转换 ====================

    /**
     * 将request参数值转为Map
     */
    public static Map<String, Object> requestToMap(HttpServletRequest request) {
        Map<String, Object> requestMap = new HashMap<>();
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] pv = request.getParameterValues(paramName);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < pv.length; i++) {
                if (pv[i].length() > 0) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    sb.append(pv[i]);
                }
            }
            requestMap.put(paramName, sb.toString());
        }
        return requestMap;
    }

    /**
     * 将request转Map并且验证非空字段
     */
    public static Map<String, Object> requestToMapAndValidate(
            HttpServletRequest request, String requiredColumns) {
        Map<String, Object> requestMap = requestToMap(request);
        ValidationUtils.validateRequiredFields(requestMap, requiredColumns);
        return requestMap;
    }

    // ==================== 请求信息获取 ====================

    /**
     * 获取客户端真实IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        String unknownIp = "unknown";
        
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isEmpty(ip) || unknownIp.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isEmpty(ip) || unknownIp.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isEmpty(ip) || unknownIp.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isEmpty(ip) || unknownIp.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isEmpty(ip) || unknownIp.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个
        if (StringUtils.isNotEmpty(ip) && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /**
     * 获取完整的请求URL（包含查询参数）
     */
    public static String getFullRequestUrl(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        
        if (StringUtils.isNotEmpty(queryString)) {
            return uri + "?" + queryString;
        }
        return uri;
    }
}