package com.geeksec.services.common.shared.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * JSON 响应工具类
 *
 * 提供标准化的 JSON 响应格式处理：
 * - 成功响应封装
 * - 错误响应封装
 * - JSON 解析
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class JsonResponseUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private JsonResponseUtils() {
        // 工具类，禁止实例化
    }

    // ==================== 成功响应 ====================

    /**
     * 返回成功的JSON响应（无数据）
     */
    public static Map<String, Object> success() {
        return success(new HashMap<>());
    }

    /**
     * 返回成功的JSON响应（带数据）
     */
    public static Map<String, Object> success(Object data) {
        Map<String, Object> result = new HashMap<>();
        result.put("err", 0);
        result.put("msg", "成功");
        result.put("data", data);
        return result;
    }

    // ==================== 错误响应 ====================

    /**
     * 返回错误的JSON响应
     */
    public static Map<String, Object> error(Integer err, String msg) {
        Map<String, Object> result = new HashMap<>();
        result.put("err", err);
        result.put("msg", msg);
        result.put("data", new HashMap<>());
        return result;
    }

    /**
     * 返回错误的JSON响应（默认错误码）
     */
    public static Map<String, Object> error(String msg) {
        return error(40000, msg);
    }

    // ==================== JSON 解析 ====================

    /**
     * 安全的JSON解析为Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> parseToMap(String jsonStr) {
        try {
            return OBJECT_MAPPER.readValue(jsonStr, Map.class);
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", jsonStr, e);
            return new HashMap<>();
        }
    }

    /**
     * 安全的JSON解析为指定类型
     */
    public static <T> T parseToObject(String jsonStr, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(jsonStr, clazz);
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", jsonStr, e);
            return null;
        }
    }

    /**
     * 对象转JSON字符串
     */
    public static String toJsonString(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            log.warn("对象转JSON失败: {}", obj, e);
            return "{}";
        }
    }
}