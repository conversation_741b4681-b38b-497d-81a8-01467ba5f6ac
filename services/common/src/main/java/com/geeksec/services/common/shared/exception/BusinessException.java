package com.geeksec.services.common.shared.exception;

import com.geeksec.services.common.shared.enums.ErrorCode;

/**
 * 业务异常
 *
 * 用于表示业务逻辑错误的异常
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误代码
     */
    private final String errorCode;
    
    /**
     * 构造函数
     *
     * @param errorCode 错误代码枚举
     */
    public BusinessException(ErrorCode errorCode) {
        this(errorCode.name(), errorCode.getMessage());
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        this("BUSINESS_ERROR", message);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     */
    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param message 错误消息
     * @param cause 原因
     */
    public BusinessException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    /**
     * 获取错误代码
     * 
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }
}
