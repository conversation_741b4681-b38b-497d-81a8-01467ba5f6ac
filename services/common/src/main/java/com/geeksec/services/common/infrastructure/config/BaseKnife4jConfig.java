package com.geeksec.services.common.infrastructure.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

/**
 * Knife4j API 文档配置基类
 *
 * 各服务可以继承此类并自定义服务特定的 API 文档信息
 * 基于 Knife4j 4.5.0 和 OpenAPI 3.0 规范
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public abstract class BaseKnife4jConfig {

    @Value("${spring.application.name:nta-service}")
    private String serviceName;

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    /**
     * 创建 OpenAPI 配置
     *
     * @return OpenAPI 实例
     */
    protected OpenAPI createOpenAPI() {
        return new OpenAPI()
                .info(createApiInfo())
                .servers(createServers());
    }

    /**
     * 创建 API 信息
     * 子类可以重写此方法来自定义 API 信息
     *
     * @return API 信息
     */
    protected Info createApiInfo() {
        return new Info()
                .title(getApiTitle())
                .description(getApiDescription())
                .version(getApiVersion())
                .contact(createContact())
                .license(createLicense());
    }

    /**
     * 创建服务器列表
     *
     * @return 服务器列表
     */
    protected List<Server> createServers() {
        String serverUrl = "http://localhost:" + serverPort;
        if (contextPath != null && !contextPath.isEmpty()) {
            serverUrl += contextPath;
        }

        Server server = new Server()
                .url(serverUrl)
                .description("本地开发服务器");

        return List.of(server);
    }

    /**
     * 创建联系人信息
     *
     * @return 联系人信息
     */
    protected Contact createContact() {
        return new Contact()
                .name("NTA 开发团队")
                .email("<EMAIL>")
                .url("https://www.geeksec.com");
    }

    /**
     * 创建许可证信息
     *
     * @return 许可证信息
     */
    protected License createLicense() {
        return new License()
                .name("Apache 2.0")
                .url("https://www.apache.org/licenses/LICENSE-2.0.html");
    }

    /**
     * 获取 API 标题
     * 子类必须实现此方法
     *
     * @return API 标题
     */
    protected abstract String getApiTitle();

    /**
     * 获取 API 描述
     * 子类必须实现此方法
     *
     * @return API 描述
     */
    protected abstract String getApiDescription();

    /**
     * 获取 API 版本
     * 子类可以重写此方法来自定义版本
     *
     * @return API 版本
     */
    protected String getApiVersion() {
        return "3.0.0";
    }

    /**
     * 获取服务名称
     *
     * @return 服务名称
     */
    protected String getServiceName() {
        return serviceName;
    }
}
