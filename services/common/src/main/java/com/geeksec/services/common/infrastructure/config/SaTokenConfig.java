package com.geeksec.services.common.infrastructure.config;
//
//import cn.dev33.satoken.interceptor.SaInterceptor;
//import cn.dev33.satoken.router.SaRouter;
//import cn.dev33.satoken.stp.StpUtil;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
///**
// * Sa-Token 权限认证配置类
// *
// * 统一配置 Sa-Token 权限认证框架：
// * - 路由拦截器
// * - 权限校验规则
// * - 白名单配置
// *
// * <AUTHOR> Team
// * @since 3.0.0
// */
//@Configuration
//public class SaTokenConfig implements WebMvcConfigurer {
//
//    /**
//     * 注册 Sa-Token 拦截器
//     */
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验
//        registry.addInterceptor(new SaInterceptor(handle -> {
//            SaRouter
//                // 拦截所有路由
//                .match("/**")
//                // 排除登录接口
//                .notMatch("/auth/login", "/auth/register", "/auth/captcha")
//                // 排除静态资源
//                .notMatch("/static/**", "/favicon.ico")
//                // 排除 Swagger 文档
//                .notMatch("/doc.html", "/webjars/**", "/v3/api-docs/**", "/swagger-resources/**")
//                // 排除健康检查
//                .notMatch("/actuator/**", "/health/**")
//                // 排除错误页面
//                .notMatch("/error")
//                // 执行登录校验
//                .check(r -> StpUtil.checkLogin());
//        })).addPathPatterns("/**");
//    }
//
//    /**
//     * 权限校验白名单
//     */
//    public static final String[] AUTH_WHITELIST = {
//        // 认证相关接口
//        "/auth/login",
//        "/auth/register",
//        "/auth/captcha",
//        "/auth/refresh",
//
//        // 静态资源
//        "/static/**",
//        "/favicon.ico",
//
//        // Swagger 文档
//        "/doc.html",
//        "/webjars/**",
//        "/v3/api-docs/**",
//        "/swagger-resources/**",
//        "/swagger-ui/**",
//
//        // 健康检查
//        "/actuator/**",
//        "/health/**",
//
//        // 错误页面
//        "/error"
//    };
//
//    /**
//     * 管理员权限标识
//     */
//    public static final String ADMIN_PERMISSION = "admin";
//
//    /**
//     * 用户权限标识
//     */
//    public static final String USER_PERMISSION = "user";
//
//    /**
//     * 只读权限标识
//     */
//    public static final String READONLY_PERMISSION = "readonly";
//}
