package com.geeksec.common.util;

import com.geeksec.services.common.shared.dto.PageResultVo;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工具类重构测试
 * 验证新的工具类功能是否正常工作
 */
class UtilsRefactoringTest {

    @Test
    void testPageResultVo() {
        // 测试分页结果创建
        List<String> data = Arrays.asList("item1", "item2", "item3");
        PageResultVo<String> pageResult = PageResultVo.of(data, 25, 1, 10);

        assertNotNull(pageResult);
        assertEquals(data, pageResult.getRecords());
        assertEquals(25, pageResult.getTotal());
        assertEquals(1, pageResult.getCurrent());
        assertEquals(10, pageResult.getSize());
        assertEquals(3, pageResult.getPages());
        assertTrue(pageResult.isHasNext());
        assertFalse(pageResult.isHasPrevious());

        // 测试空分页结果
        PageResultVo<String> emptyResult = PageResultVo.empty(1, 10);
        assertNotNull(emptyResult);
        assertEquals(0, emptyResult.getTotal());
        assertTrue(emptyResult.getRecords().isEmpty());
    }
}