package com.geeksec.graph.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.services.common.shared.enums.ErrorCode;
import com.geeksec.services.common.shared.exception.BusinessException;
import com.geeksec.services.common.shared.utils.CommonUtil;
import com.geeksec.services.common.shared.utils.IpUtils;
import com.geeksec.graph.condition.NbLabelUpCondition;
import com.geeksec.graph.condition.NbRemarkUpCondition;
import com.geeksec.graph.dao.TagInfoDao;
import com.geeksec.graph.dao.TaskAnalysisDao;
import com.geeksec.graph.entity.TaskAnalysis;
import com.geeksec.graph.entity.TbTagInfo;
import com.geeksec.graph.pojo.vertex.AppVertex;
import com.geeksec.graph.pojo.vertex.SslFingerVertex;
import com.geeksec.graph.repository.*;
import com.geeksec.graph.service.*;
import com.geeksec.graph.vo.*;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class Metadata2ServiceImpl implements Metadata2Service {

    @Autowired
    private TaskAnalysisDao taskAnalysisDao;

    @Autowired
    private TagInfoDao tagInfoDao;

    @Autowired
    private AllAtlasDao allAtlasDao;

    @Autowired
    private IpService ipService;

    @Autowired
    private CertService certService;

    @Autowired
    private SslFingerDao sslFingerDao;

    @Autowired
    private DomainService domainService;

    @Autowired
    private EdgeTypeService edgeTypeService;

    @Autowired
    private AppServiceDao appServiceDao;

    @Autowired
    private AppDao appDao;

    @Autowired
    private OrgDao orgDao;

    @Override
    public ApiResponse getIpInfo(String ip) {
        IpInfoVo ipInfoVo = new IpInfoVo();
        ipInfoVo.setIp(ip);
        Map<String, Object> ipTagMap = ipService.getIpInfo(ip);
        if (MapUtil.isEmpty(ipTagMap)) {
            return ApiResponse.success("IP详情正在准备中...");
        }
        delIpTag(ipInfoVo, ipTagMap);

        //开放服务：  服务--IP-->服务归属
        IpCountVo openPortIpCountVo = ipService.countAppServerByIp(ip);
        if (openPortIpCountVo != null) {
            ipInfoVo.setOpenPortNum(CommonUtil.convertLongToInt(openPortIpCountVo.getCount()));
        }
        //访问端口数：  IP--服务-->访问服务
        IpCountVo accessPortIpCountVo = ipService.countClientAppByIp(ip);
        if (accessPortIpCountVo != null) {
            ipInfoVo.setAccessPortNum(CommonUtil.convertLongToInt(accessPortIpCountVo.getCount()));
        }
        //关联锚域名
        //先查IP->关联域名
        Set<String> relatedDomainList = new HashSet<>();

        // parse_to 最终解析
        IpRelatedDomainsVo parseToRelatedDomain = ipService.listParseToRelatedDomainsByIp(ip);
        if (parseToRelatedDomain != null) {
            relatedDomainList.addAll(parseToRelatedDomain.getDomainList());
        }

        IpRelatedDomainsVo serverRelatedDomain = ipService.listServerRelatedDomainsByIp(ip);
        if (serverRelatedDomain != null) {
            relatedDomainList.addAll(serverRelatedDomain.getDomainList());
        }

        if (!CollectionUtils.isEmpty(relatedDomainList)) {
            Integer count = ipService.countFDomainNumByDomains(relatedDomainList);
            ipInfoVo.setFDomainNum(count);
        } else {
            // 不存在所属锚域名数
            ipInfoVo.setFDomainNum(0);
        }

        //关联标签IP--标签-->（只有一个边）
        List<Long> labelList = ipService.listHasLabelByIp(ip);
        List<Integer> labels = new ArrayList<>();
        for (Long lable : labelList) {
            labels.add(CommonUtil.convertLongToInt(lable));
        }
        ipInfoVo.setLabels(labels);
        String ipKey = ipInfoVo.getIpKey();
        // 内网IP情况 展示任务名称
        if (ipKey.contains("-")) {
            TaskAnalysis task = taskAnalysisDao.getTaskAnalysis(Integer.valueOf(ipKey.split("-")[0]));
            ipInfoVo.setTaskNames(Lists.newArrayList(task.getTaskName()));
        } else {
            ipInfoVo.setTaskNames(new ArrayList<>());
        }
        return ApiResponse.success(ipInfoVo);
    }

    private void delIpTag(IpInfoVo ipInfoVo, Map<String, Object> ipTagMap) {
        ipInfoVo.setIpAddr(ipTagMap.get("ipAddr").toString());
        ipInfoVo.setIpKey(ipTagMap.get("ipKey").toString());
        Object city = ipTagMap.get("city");
        if (city != null) {
            ipInfoVo.setCity(city.toString());

        }
        Object country = ipTagMap.get("country");
        if (city != null) {
            ipInfoVo.setCountry(country.toString());
        }
        ipInfoVo.setAverageBps(Math.abs(Long.parseLong(ipTagMap.get("averageBps").toString())));
        ipInfoVo.setRecvBytes(Math.abs(Long.parseLong(ipTagMap.get("recvBytes").toString())));
        ipInfoVo.setSendBytes(Math.abs(Long.parseLong(ipTagMap.get("sendBytes").toString())));
        ipInfoVo.setFirstTime(Long.parseLong(ipTagMap.get("firstTime").toString()));
        ipInfoVo.setLastTime(Long.parseLong(ipTagMap.get("lastTime").toString()));
        ipInfoVo.setBlackList(Integer.parseInt(ipTagMap.get("blackList").toString()));
        ipInfoVo.setWhiteList(Integer.parseInt(ipTagMap.get("whiteList").toString()));

        Object remark = ipTagMap.get("remark");
        if (remark != null) {
            String s = remark.toString();
            ipInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                ipInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                ipInfoVo.setRemarks(list);
            }
        } else {
            ipInfoVo.setRemarks(new ArrayList<>());
        }
    }

    @Override
    public ApiResponse<OrgInfoVo> getOrgInfo(String org) {
        OrgInfoVo orgInfoVo = new OrgInfoVo();
        OrgVo orgVo = orgDao.getOrg(org);
        BeanUtil.copyProperties(orgVo,orgInfoVo);
        if (orgInfoVo == null) {
            log.info("企业详情，nebula中无返回，org={}", org);
            throw new BusinessException(ErrorCode.GRAPHDB_QUERY_EMPTY);
        }
        Object remark = orgVo.getRemark();
        if (remark != null) {
            String s = remark.toString();
            orgInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                orgInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                orgInfoVo.setRemarks(list);
            }
        } else {
            orgInfoVo.setRemarks(new ArrayList<>());
        }
        return ApiResponse.success(orgInfoVo);
    }

    @Override
    public ApiResponse<SSLFingerInfoVo> getSSLFingerInfo(String str) {
        SSLFingerInfoVo sslFingerInfoVo = new SSLFingerInfoVo();

        SslFingerVertex sslFingerVertex = sslFingerDao.selectById(str);
        if (sslFingerVertex == null) {
            log.info("指纹详情，nebula中无返回，str={}", str);
            throw new BusinessException(ErrorCode.GRAPHDB_QUERY_EMPTY);
        }
        // 通过BeanUtils.copyProperties()将sslFingerVertex的属性拷贝到sslFingerInfoVo
        BeanUtil.copyProperties(sslFingerVertex, sslFingerInfoVo);

        List<List<String>> labelList = allAtlasDao.getTagLabels(str);
        List<Integer> labelIds = Lists.newArrayListWithCapacity(labelList.size());

        if (CollectionUtils.isNotEmpty(labelList)) {
            for (List<String> labels : labelList) {
                for (String label : labels) {
                    labelIds.add(Integer.parseInt(label));
                }
            }
        }

        sslFingerInfoVo.setLabels(labelIds);
        return ApiResponse.success(sslFingerInfoVo);
    }

    @Override
    public ApiResponse<AppServiceInfoVo> getAppService(String str) {
        AppServiceInfoVo appServiceInfoVo = new AppServiceInfoVo();
        AppServiceVo appServiceVertex = appServiceDao.getAppService(str);
        if (appServiceVertex == null) {
            log.info("应用服务详情，nebula中无返回，str={}", str);
            throw new BusinessException(ErrorCode.GRAPHDB_QUERY_EMPTY);
        }
        BeanUtil.copyProperties(appServiceVertex, appServiceInfoVo);
        appServiceInfoVo.setAppName(appServiceVertex.getAppName());
        appServiceInfoVo.setDPort(appServiceVertex.getDPort());
        appServiceInfoVo.setIpPro(appServiceVertex.getIPPro());
        return ApiResponse.success(appServiceInfoVo);
    }

    @Override
    public ApiResponse<AppInfoVo> getApp(String str) {
        AppInfoVo appInfoVo = new AppInfoVo();
        AppVertex appVertex = appDao.selectById(str);
        if (appVertex == null) {
            log.info("应用详情，nebula中无返回，str={}", str);
            throw new BusinessException(ErrorCode.GRAPHDB_QUERY_EMPTY);
        }
        appInfoVo.setAppName(appVertex.getAppName());
        appInfoVo.setAppVersion(appVertex.getAppVersion());
        List<List<String>> labelList = allAtlasDao.getTagLabels(str);
        List<Integer> labelIds = Lists.newArrayListWithCapacity(labelList.size());

        if (CollectionUtils.isNotEmpty(labelList)) {
            for (List<String> labels : labelList) {
                for (String label : labels) {
                    labelIds.add(Integer.parseInt(label));
                }
            }
        }

        appInfoVo.setLabels(labelIds);
        return ApiResponse.success(appInfoVo);
    }

    /**
     * 证书详情分为四个部分 基础信息basic_data、证书日志log_data、签发链sign_chain、图探索atlas_data
     * 本接口只返回其前三个
     *
     * @param certId
     * @return
     */
    @Override
    public ApiResponse getCertDetail(String certId) {
        Map<String, Object> resultMap = new HashMap<>();

        // 首次出现时间和末次出现时间在Nebula里面
        Map<String, Object> certTagMap = certService.getCertInfo(certId);
        //todo
        /*// 一次性将CERT在ES中的数据查询出
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(1).from(0);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("ASN1SHA1", certId));
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest("cert_user").source(searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long totalHits = searchResponse.getHits().totalHits;
        if (totalHits == 0) {
            log.warn("查询证书详情信息失败,证书SHA1:{}不存在", certId);
            return ApiResponse.success("证书详情信息正在准备中，请稍后...");
        }
        SearchHit[] hits = searchResponse.getHits().getHits();
        Map<String, Object> sourceAsMap = new HashMap<>();
        String index = "";
        // 证书信息以cert_user的为准
        for (SearchHit hit : hits) {
            String hitIndex = hit.getIndex();
            if ("cert_user".equals(hitIndex)) {
                sourceAsMap = hit.getSourceAsMap();
                index = hitIndex;
                break;
            }
            if ("cert_system".equals(hitIndex)) {
                sourceAsMap = hit.getSourceAsMap();
                index = hitIndex;
            }
        }

        // 获取标签实体的信息
        List<String> labels = (List<String>) sourceAsMap.get("Labels");
        if (CollectionUtils.isEmpty(labels) && "cert_system".equals(index)) {
            labels.add("228");
        }

        // 基础信息组装
        CertInfoVo certInfoVo = assembleCertInfoVo(certId, certTagMap, sourceAsMap, labels);
        resultMap.put("basic_data", certInfoVo);

        // 证书日志(纯ES日志)
        sourceAsMap.put("es_id", hits[0].getId());
        resultMap.put("log_data", sourceAsMap);

        // 签发链数据
        // 遍历循环查询证书签发链信息 ["父亲证书"，"爷爷证书"，"太爷爷证书"]，依次按顺序查询
        List<Map<String, Object>> signCertList = new ArrayList<>();
        List<String> fatherCertIds = new ArrayList<>();
        Map<String, Object> certChainMap = new HashMap<>();

        certChainMap.put("cert_sha1", certId);
        certChainMap.put("Labels", transCertTagInfoVoList(labels));
        signCertList.add(certChainMap);

        fatherCertIds.addAll((Collection<? extends String>) sourceAsMap.getOrDefault("FatherCertIDList", new ArrayList<>()));
        if (CollectionUtil.isNotEmpty(fatherCertIds)) {
            for (String fatherCertId : fatherCertIds) {
                Map<String, Object> signCertInfo = getSignCertInfo(fatherCertId);
                signCertList.add(signCertInfo);
            }
        }
        resultMap.put("sign_chains", signCertList);*/

        return ApiResponse.success(resultMap);
    }

    /**
     * 通过信息组装CertInfoVo详情基础信息
     *
     * @param certId
     * @param certTagMap
     * @param sourceAsMap
     * @param labels
     * @return
     */
    private CertInfoVo assembleCertInfoVo(String certId, Map<String, Object> certTagMap, Map<String, Object> sourceAsMap, List<String> labels) {
        CertInfoVo certInfoVo = new CertInfoVo();
        certInfoVo.setCert(certId);
        certInfoVo.setLabels(labels.stream().map(Integer::parseInt).collect(Collectors.toList()));
        certInfoVo.setCertTagList(transCertTagInfoVoList(labels));
        // 获取父证书
        certInfoVo.setFatherIdList((List<String>) sourceAsMap.getOrDefault("FatherCertIDList", new ArrayList<>()));

        //服务器热度 IP——证书——》服务端使用证书server_use_cert
        List<String> serverIps = certService.listRelatedServerIpsByCert(certId);
        certInfoVo.setServerHeat(IpUtils.ipHotCrc(serverIps));
        //客户端热度 IP——证书——》客户端使用证书server_use_cert
        List<String> clientIps = certService.listRelatedClientIpsByCert(certId);
        certInfoVo.setClientHeat(IpUtils.ipHotCrc(clientIps));
        Map<String, Object> issuerMap = (Map<String, Object>) sourceAsMap.get("Issuer");
        if (MapUtils.isNotEmpty(issuerMap)) {
            // 签发机构
            certInfoVo.setIssuerO((String) issuerMap.get("CN"));
        } else {
            certInfoVo.setIssuerO("");
        }
        Map<String, Object> subjectMap = (Map<String, Object>) sourceAsMap.get("Subject");
        if (MapUtils.isNotEmpty(subjectMap)) {
            // 证书所有者
            certInfoVo.setSubjectO((String) subjectMap.get("CN"));
        } else {
            certInfoVo.setSubjectO("");
        }

        //  "NotBefore": "20161207121734",转换为2016-12-07 12:17:34
        String notBefore = (String) sourceAsMap.get("NotBefore");
        if (StringUtils.isNotEmpty(notBefore)) {
            certInfoVo.setNotBefore(notBefore.substring(0, 4) + "-" + notBefore.substring(4, 6) + "-" + notBefore.substring(6, 8) + " " + notBefore.substring(8, 10) + ":" + notBefore.substring(10, 12) + ":" + notBefore.substring(12, 14));
        } else {
            certInfoVo.setNotBefore("");
        }

        // "NotAfter": "20201207121734",转换为2020-12-07 12:17:34
        String notAfter = (String) sourceAsMap.get("NotAfter");
        if (StringUtils.isNotEmpty(notAfter)) {
            certInfoVo.setNotAfter(notAfter.substring(0, 4) + "-" + notAfter.substring(4, 6) + "-" + notAfter.substring(6, 8) + " " + notAfter.substring(8, 10) + ":" + notAfter.substring(10, 12) + ":" + notAfter.substring(12, 14));
        } else {
            certInfoVo.setNotAfter("");
        }

        // 首次&末次出现时间
        certInfoVo.setFirstTime(Integer.parseInt(certTagMap.get("firstTime").toString()));
        certInfoVo.setLastTime(Integer.parseInt(certTagMap.get("lastTime").toString()));
        Object remark = certTagMap.get("remark");
        if (remark != null) {
            String s = remark.toString();
            certInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                certInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                certInfoVo.setRemarks(list);
            }
        } else {
            certInfoVo.setRemarks(new ArrayList<>());
        }

        return certInfoVo;
    }

    /**
     * 通过获取到的标签集合转化为标签实体VO信息并赋予证书标签的等级
     *
     * @param labels
     * @return
     */
    private List<CertTagVo> transCertTagInfoVoList(List<String> labels) {
        List<CertTagVo> certTagVos = new ArrayList<>(labels.size());

        QueryWrapper<TbTagInfo> queryWrapper = new QueryWrapper<>();
        List<Integer> tagIdList = labels.stream().map(Integer::parseInt).collect(Collectors.toList());
        queryWrapper.in("tag_id", tagIdList).eq("tag_target_type", 4);
        List<TbTagInfo> tagInfoList = tagInfoDao.selectList(queryWrapper);

        for (TbTagInfo tagInfo : tagInfoList) {
            CertTagVo certTagVo = new CertTagVo();
            certTagVo.setTagId(tagInfo.getTagId());
            certTagVo.setTagText(tagInfo.getTagText());
            certTagVo.setTagRemark(tagInfo.getTagRemark());
            certTagVo.setBlackList(tagInfo.getBlackList());
            certTagVo.setWhiteList(tagInfo.getWhiteList());
            int blackList = tagInfo.getBlackList();
            int whiteList = tagInfo.getWhiteList();
            // 判断标签等级
            if (blackList >= 1 && blackList <= 100 && whiteList != 100) {
                certTagVo.setTagLevel(blackList >= 80 ? "danger" : "warning");
            } else if (whiteList >= 1 && whiteList <= 100 && blackList == 0) {
                certTagVo.setTagLevel(whiteList == 100 ? "success" : "positive");
            } else {
                certTagVo.setTagLevel("info");
            }
            certTagVos.add(certTagVo);
        }
        return certTagVos;
    }

    private void delCertTag(Map<String, Object> certTagMap, CertInfoVo certInfoVo) {
        certInfoVo.setFirstTime(Integer.parseInt(certTagMap.get("firstTime").toString()));
        certInfoVo.setLastTime(Integer.parseInt(certTagMap.get("lastTime").toString()));
        certInfoVo.setBlackList(Integer.parseInt(certTagMap.get("blackList").toString()));
        certInfoVo.setWhiteList(Integer.parseInt(certTagMap.get("whiteList").toString()));
        Object remark = certTagMap.get("remark");
        if (remark != null) {
            String s = remark.toString();
            certInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                certInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                certInfoVo.setRemarks(list);
            }
        } else {
            certInfoVo.setRemarks(new ArrayList<>());
        }

    }


    @Override
    public ApiResponse getDomainInfo(String domain) {
        DomainInfoVo domainInfoVo = new DomainInfoVo();
        domainInfoVo.setDomain(domain);
        Map<String, Object> domainTagMap = domainService.getDomainInfo(domain);
        delDomainInfo(domainTagMap, domainInfoVo);
        //自身标签
        List<Long> labelList = domainService.listHasLabelByDomain(domain);
        List<Integer> labels = new ArrayList<>();
        for (Long lable : labelList) {
            labels.add(CommonUtil.convertLongToInt(lable));
        }
        domainInfoVo.setLabels(labels);
        //锚域名
        String fDomain = domainService.getFDomainByDomain(domain);
        if (StringUtils.isNotEmpty(fDomain)) {
            domainInfoVo.setFDomain(fDomain);
            //域名--标签-->（只有一个边）
            List<Long> fLabelList = domainService.listHasLabelByFDomain(fDomain);
            List<Integer> fLabels = new ArrayList<>();
            for (Long fLable : fLabelList) {
                fLabels.add(CommonUtil.convertLongToInt(fLable));
            }
            domainInfoVo.setFLabels(fLabels);
            //兄弟域名 锚域名 反查 域名个数  -1
            DomainCountVo domainCountVo = domainService.countBrotherNumByDomain(fDomain);
            if (domainCountVo != null && domainCountVo.getCount() != 0) {
                domainInfoVo.setBrotherNum(domainCountVo.getCount() - 1);
            }
        }
        //域名指向IP数量  域名--IP--》解析(cname_result)  最终解析（parse_to）这两个边相加
        Integer cnameResultCount = domainService.countCnameResultByDomain(domain);
        Integer parseToCount = domainService.countParseToByDomain(domain);
        domainInfoVo.setToIpNum(cnameResultCount + parseToCount);
        // 客户端热度，IP--域名-->HTTP访问和SSL访问
        DomainRelatedIpsVo domainRelatedIpsVo = domainService.listRelatedClientIpsByDomain(domain);
        if (domainRelatedIpsVo != null && domainRelatedIpsVo.getIpList().size() > 0) {
            Set<String> ips = new HashSet<>(domainRelatedIpsVo.getIpList());
            //ip请求热度的算法
            domainInfoVo.setClientHeat(IpUtils.ipHotCrc(new ArrayList<>(ips)));
        }
        //关联证书 DOMAIN - CERT --》sni_bind
        Integer certNum = domainService.countCertNumByDomain(domain);
        domainInfoVo.setCertNum(certNum);
        return ApiResponse.success(domainInfoVo);
    }

    private void delDomainInfo(Map<String, Object> domainTagMap, DomainInfoVo domainInfoVo) {
        domainInfoVo.setFirstTime(Integer.parseInt(domainTagMap.get("firstTime").toString()));
        domainInfoVo.setLastTime(Integer.parseInt(domainTagMap.get("lastTime").toString()));
        domainInfoVo.setBlackList(Integer.parseInt(domainTagMap.get("blackList").toString()));
        domainInfoVo.setWhiteList(Integer.parseInt(domainTagMap.get("whiteList").toString()));
        Object remark = domainTagMap.get("remark");
        if (remark != null) {
            String s = remark.toString();
            domainInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                domainInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                domainInfoVo.setRemarks(list);
            }
        } else {
            domainInfoVo.setRemarks(new ArrayList<>());
        }

        Object alexaRankObj = domainTagMap.get("alexaRank");
        if (alexaRankObj != null) {
            domainInfoVo.setAlexaRank(Integer.parseInt(alexaRankObj.toString()));
        }
        domainInfoVo.setWhoIs(domainTagMap.get("whoIs").toString());
    }

    @Override
    public ApiResponse updateLabels(NbLabelUpCondition condition) {
        log.info("修改标签，condition={}", condition);

        String type = condition.getType();
        if (StringUtils.isEmpty(type) || !validType(type)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_LEAK);
        }

        String str = condition.getStr();
        List<String> labels = condition.getLabels();

        // 如果为证书的标签修改，走一遍ES的修改
        if ("CERT".equals(type)) {
            if (labels.contains("228") && labels.contains("182")) {
                log.error("证书标签中不能同时含有黑名单和白名单标签");
                throw new BusinessException(ErrorCode.CERT_BLACK_WHITE_REPEAT);
            }
            //todo
            /*// 以ID为条件，查询出当前需要修改目标ES元数据信息
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.termQuery("ASN1SHA1", str));
            searchSourceBuilder.query(boolQueryBuilder);
            // 全量的索引中进行查找更新
            SearchRequest searchRequest = new SearchRequest(new String[]{"cert_user"}, searchSourceBuilder);
            SearchResponse searchResponse = esearchService.esSearch(searchRequest);
            long totalHits = searchResponse.getHits().totalHits;
            if (totalHits == 0) {
                log.error("修改目标标签失败！查询对应doc失败");
                throw new BusinessException(ErrorCode.CERT_TAG_MODIFY_EMPTY);
            }
            SearchHit[] hits = searchResponse.getHits().getHits();
            Map<String, Object> esDataMap = hits[0].getSourceAsMap();
            String esId = hits[0].getId();

            esDataMap.put("Labels", labels);
            try {
                // 开始执行更新操作
                UpdateRequest updateRequest = new UpdateRequest("cert_user", "_doc", esId);
                updateRequest.fetchSource(true);
                updateRequest.doc(esDataMap);
                UpdateResponse updateResponse = esearchService.updateDoc(updateRequest);
                // 在此处等待ES数据请求处理
                Thread.sleep(500);
                RestStatus status = updateResponse.status();
                if (status.getStatus() == 200) {
                    return ApiResponse.success("更新目标标签成功！");
                } else {
                    throw new BusinessException(ErrorCode.CERT_TAG_MODIFY_ERROR);
                }
            } catch (Exception e) {
                log.error("更新目标标签失败！", e);
                throw new BusinessException(ErrorCode.CERT_TAG_MODIFY_ERROR);
            }*/
        }
        // 全量删除后，进行全量新增
        //1.删除Label边
        edgeTypeService.deleteHasLabel(str);

        if (CollectionUtil.isNotEmpty(labels)) {
            //2.重新插入
            edgeTypeService.insertHasLabel(str, labels.stream().map(Object::toString).collect(Collectors.toList()));
        }

        return ApiResponse.success("修改标签成功");
    }

    private boolean validType(String type) {
        List<String> validTypes = Arrays.asList("IP", "CERT", "DOMAIN", "ORG", "BLOCKCHAIN", "SSLFINGER","APP");
        return validTypes.contains(type);
    }

    @Override
    public ApiResponse updateRemark(NbRemarkUpCondition condition) {
        log.info("修改备注，condition={}", JSON.toJSONString(condition));
        String type = condition.getType();
        if (StringUtils.isEmpty(type)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        if (!("IP".equals(type) || "CERT".equals(type) || "DOMAIN".equals(type) || "ORG".equals(type))) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        List<String> remarks = condition.getRemarks();
        String remark = "[]";
        if (remarks != null && !remarks.isEmpty()) {
            remark = JSON.toJSONString(remarks);
        }
        ipService.updateRemark(condition.getType(), condition.getStr(), remark);
        return ApiResponse.success("修改备注成功");
    }

}
