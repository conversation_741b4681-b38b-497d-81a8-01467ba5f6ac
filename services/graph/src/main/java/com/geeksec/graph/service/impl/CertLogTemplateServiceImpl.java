package com.geeksec.graph.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.graph.dao.CertLogTemplateDao;
import com.geeksec.graph.entity.CertLogTemplate;
import com.geeksec.graph.service.CertLogTemplateService;
import com.geeksec.services.common.shared.enums.ErrorCode;
import com.geeksec.services.common.shared.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: jerryzhou
 * @date: 2024/1/29 18:10
 * @Description:
 **/
@Service
public class CertLogTemplateServiceImpl implements CertLogTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(CertLogTemplateServiceImpl.class);

    @Autowired
    private CertLogTemplateDao certLogTemplateDao;

    @Override
    public ApiResponse getUserTemplate(Integer userId) {
        logger.info("查询当前用户的证书详情日志模板,user_id->{}", userId);

        // 先查询是否存在当前用户的模板样式
        try {
            CertLogTemplate certLogTemplate = certLogTemplateDao.selectById(userId);
            if (certLogTemplate == null) {
                // 不存在则插入默认返回样式
                String defaultTemplateJson = parseFile("dict/cert_log_template.json");
                // 将jsonStr 转化为Map<String,Object>
                certLogTemplate = new CertLogTemplate();
                certLogTemplate.setUserId(userId);
                certLogTemplate.setTemplateJson(defaultTemplateJson);
                certLogTemplateDao.insert(certLogTemplate);
                // 返回默认样式
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> templateMap = objectMapper.readValue(defaultTemplateJson, LinkedHashMap.class);
                return ApiResponse.success(templateMap);
            } else {
                // 存在则返回当前用户的样式
                String templateJson = certLogTemplate.getTemplateJson();
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> templateMap = objectMapper.readValue(templateJson, LinkedHashMap.class);
                return ApiResponse.success(templateMap);
            }

        } catch (Exception e) {
            logger.info("获取当前用户的证书详情日志模板失败！error->{}", e);
            throw new BusinessException(ErrorCode.GET_USER_CERT_LOG_ERROR);
        }
    }

    @Override
    public ApiResponse modifyUserTemplate(Integer userId, String key, String value) {
        logger.info("修改当前用户的证书详情日志模板,user_id->{}", userId);
        try {
            CertLogTemplate certLogTemplate = certLogTemplateDao.selectById(userId);
            if (certLogTemplate == null){
                throw new BusinessException(ErrorCode.GET_USER_CERT_LOG_ERROR);
            }
            // 存在则更新当前用户的样式
            String templateJson = certLogTemplate.getTemplateJson();
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> templateMap = objectMapper.readValue(templateJson, LinkedHashMap.class);
            if (!templateMap.containsKey(key)){
                throw new BusinessException(ErrorCode.MODIFY_USER_CERT_LOG_ERROR);
            }
            templateMap.put(key, value);
            String newTemplateJson = JSONObject.toJSONString(templateMap);
            certLogTemplate.setTemplateJson(newTemplateJson);
            certLogTemplateDao.updateById(certLogTemplate);
            return ApiResponse.success("修改证书元数据模板成功");
        } catch (Exception e) {
            logger.info("修改当前用户的证书详情日志模板失败！error->{}", e);
            throw new BusinessException(ErrorCode.GET_USER_CERT_LOG_ERROR);

        }
    }


    private String parseFile(String path) throws IOException {

        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
            return stringBuilder.toString();
        } catch (IOException e) {
            logger.error("读取path->{}文件失败！error->{}", path, e);
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ex) {
                    throw ex;
                }
            }
        }
        return null;
    }
}
