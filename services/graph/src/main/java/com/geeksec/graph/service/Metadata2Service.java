package com.geeksec.graph.service;


import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.graph.condition.NbLabelUpCondition;
import com.geeksec.graph.condition.NbRemarkUpCondition;
import com.geeksec.graph.vo.AppInfoVo;
import com.geeksec.graph.vo.AppServiceInfoVo;
import com.geeksec.graph.vo.OrgInfoVo;
import com.geeksec.graph.vo.SSLFingerInfoVo;

import java.io.UnsupportedEncodingException;

/**
 * 分析平台的会话分析
 */
public interface Metadata2Service {

    /**
     * 侧啦：IP详情
     * @param ip
     * @return
     */
    ApiResponse getIpInfo(String ip) throws UnsupportedEncodingException;

    /**
     * 证书详情 四合一
     */
    ApiResponse getCertDetail(String certId);

    /**
     * 域名详情
     * @param domain
     * @return
     */
    ApiResponse getDomainInfo(String domain) throws UnsupportedEncodingException;

    /**
     * 编辑标签（证书的标签修改含有ES修改的部分）
     * @param condition
     * @return
     */
    ApiResponse updateLabels(NbLabelUpCondition condition);

    /**
     * 编辑备注
     * @param condition
     * @return
     */
    ApiResponse updateRemark(NbRemarkUpCondition condition);

    /**
     * 获取企业信息
     * @param str
     * @return
     */
    ApiResponse<OrgInfoVo> getOrgInfo(String str);

    /**
     * 获取指纹信息
     * @param str
     * @return
     */
    ApiResponse<SSLFingerInfoVo> getSSLFingerInfo(String str);

    /**
     * 获取应用服务详情
     * @param str
     * @return
     */
    ApiResponse<AppServiceInfoVo> getAppService(String str);

    /**
     * 获取服务详情
     * @param str
     * @return
     */
    ApiResponse<AppInfoVo> getApp(String str);

}
