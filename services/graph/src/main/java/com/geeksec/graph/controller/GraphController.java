package com.geeksec.graph.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.services.common.shared.dto.ApiResponse;
import com.geeksec.services.common.shared.controller.BaseController;
import com.geeksec.graph.condition.*;
import com.geeksec.graph.constants.AtlasTypeConstants;
import com.geeksec.graph.service.AtlasService;
import com.geeksec.graph.service.CertLogTemplateService;
import com.geeksec.graph.service.Metadata2Service;
import com.geeksec.graph.service.VertexService;
import com.geeksec.graph.vo.*;
import com.geeksec.services.common.shared.enums.ErrorCode;
import com.geeksec.services.common.shared.exception.BusinessException;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 网络流量智能图谱系统控制器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@Slf4j
@RequestMapping("/analyze")
@RequiredArgsConstructor
@Tag(name = "图谱管理", description = "图谱操作")
public class GraphController extends BaseController {

    @Autowired
    private AtlasService atlasService;
    @Autowired
    private VertexService vertexService;
    @Autowired
    private Metadata2Service metadata2Service;
    @Autowired
    private CertLogTemplateService certLogTemplateService;

    @GetMapping("/search")
    @Operation(summary = "目标ID检索", description = "根据VID查询对应的第一层数据", parameters = {
            @Parameter(name = "vid_list", description = "VID列表，多个VID用逗号分隔", required = true, example = "**************,**************")
    })
    public ApiResponse<SearchVo> vidsSearch(@RequestParam("vid_list") List<String> vidList) {
        VidsSearchCondition condition = new VidsSearchCondition();
        condition.setVidList(vidList);
        // 不允许存在空字符串
        if (condition.getVidList().stream().anyMatch(StrUtil::isEmpty)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        ApiResponse resultVo = atlasService.vidSearch(condition);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            atlasService.craeteAtlasRecord(AtlasTypeConstants.SEARCH, objectMapper.writeValueAsString(condition));
        } catch (JsonProcessingException e) {
            log.error("保存探索历史失败！error->"+e.getMessage());
        }
        return resultVo;
    }

    @GetMapping("/properties/search")
    @Operation(summary = "关键词检索", description = "根据点属性查询对应的第一层数据", parameters = {
            @Parameter(name = "tag_name", description = "点名称", required = true),
            @Parameter(name = "properties_name", description = "属性名称", required = true),
            @Parameter(name = "properties_value", description = "属性值", required = true)
    })
    public ApiResponse<SearchVo> propertiesSearch(@RequestParam(name = "tag_name") String tagName, @RequestParam(name = "properties_name") String propertiesName, @RequestParam(name = "properties_value") String propertiesValue) {
        GraphPropertiesNextCondition condition  = new GraphPropertiesNextCondition();
        condition.setTagName(tagName);
        condition.setPropertiesName(propertiesName);
        condition.setPropertiesValue(propertiesValue);
        if (StringUtils.isEmpty(condition.getTagName()) || StringUtils.isEmpty(condition.getPropertiesName()) || StringUtils.isEmpty(condition.getPropertiesValue())) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        ApiResponse resultVo = atlasService.propertiesSearch(condition);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            atlasService.craeteAtlasRecord(AtlasTypeConstants.PROPERTIES_SEARCH, objectMapper.writeValueAsString(condition));
        } catch (JsonProcessingException e) {
            log.error("保存探索历史失败！error->"+e.getMessage());
        }
        return resultVo;
    }

    @GetMapping("/sub/search")
    @Operation(summary = "子图遍历", description = "进行子图遍历操作", parameters = {
            @Parameter(name = "vid_list", description = "VID列表，多个VID用逗号分隔", required = true, example = "**************,**************"),
            @Parameter(name = "step_count", description = "最大步数", required = true)
    })
    public ApiResponse<VertexAssociationNextVo> subSearch(@RequestParam(name = "vid_list") List<String> vidList,@RequestParam(name = "step_count") String stepCount) {
        SubGraphNextCondition condition = new SubGraphNextCondition();
        condition.setVidList(vidList);
        condition.setStepCount(Integer.parseInt(stepCount));
        if (CollectionUtil.isEmpty(condition.getVidList()) || condition.getStepCount()==null) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        VertexAssociationNextVo vertexAssociationNextVo = vertexService.getSubAssociationNext(condition);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            atlasService.craeteAtlasRecord(AtlasTypeConstants.SUB_SEARCH, objectMapper.writeValueAsString(condition));
        } catch (JsonProcessingException e) {
            log.error("保存探索历史失败！error->"+e.getMessage());
        }
        return ApiResponse.success(vertexAssociationNextVo);
    }

    @GetMapping("/relation/list")
    @Operation(summary = "单体TAG节点列表查询", description = "根据节点ID与类型进行单体TAG节点列表查询", parameters = {
            @Parameter(name = "search", description = "查询节点VID", required = true),
            @Parameter(name = "edge_type", description = "边类型", required = true),
            @Parameter(name = "tag_type", description = "节点类型", required = true),
            @Parameter(name = "direct", description = "正/反向(true 正向 false 反向)", required = true),
            @Parameter(name = "order_field", description = "排序字段", required = false),
            @Parameter(name = "current_page", description = "当前页", required = true),
            @Parameter(name = "page_size", description = "当前页展示数量", required = true),
            @Parameter(name = "sort_order", description = "排序方式：desc/asc", required = true)
    })
    public ApiResponse<RelationListVo> tagRelationList(@RequestParam(name = "search") String search,
                                       @RequestParam(name = "edge_type") String edgeType,
                                       @RequestParam(name = "tag_type") String tagType,
                                       @RequestParam(name = "direct") Boolean direct,
                                       @RequestParam(name = "order_field", required = false) String orderField,
                                       @RequestParam(name = "current_page", defaultValue = "1") Integer currentPage,
                                       @RequestParam(name = "page_size", defaultValue = "10") Integer pageSize,
                                       @RequestParam(name = "sort_order", defaultValue = "desc") String sortOrder
    ) {
        AtlasCondition condition = new AtlasCondition();
        condition.setSearch(search);
        condition.setEdgeType(edgeType);
        condition.setTagType(tagType);
        condition.setDirect(direct);
        condition.setOrderField(orderField);
        condition.setCurrentPage(currentPage);
        condition.setPageSize(pageSize);
        condition.setSortOrder(sortOrder);
        return atlasService.relationList(condition);
    }

    @PostMapping("/tagLabels")
    @Operation(summary = "标签节点查询", description = "查询到第一层节点后，进行标签节点查询")
    public ApiResponse<TagLAbelVo> getTagLabels(@RequestBody List<SearchLabelsCondition> params) {
        return atlasService.getTagLabels(params);
    }

    @GetMapping("/visibleRelation")
    @Operation(summary = "关联查询节点边种类", description = "关联查询节点，过滤可以展示的边种类", parameters = {
            @Parameter(name = "str", description = "查询字符串", required = true),
            @Parameter(name = "type", description = "查询类型", required = true),
            @Parameter(name = "direct", description = "方向，可选值：forward, reverse, bothway", required = true)
    })
    public ApiResponse<List<String>> getVisbleRelation(@RequestParam("str") String str,
                                         @RequestParam("type") String type,
                                         @RequestParam("direct") String direct) throws IOException {
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(type)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        if (!"forward".equals(direct) && !"reverse".equals(direct) && !"bothway".equals(direct)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }

        return atlasService.getVisibleRelation(str, type, direct);
    }

    @GetMapping("/visibleSideRelation")
    @Operation(summary = "侧拉框点边关系列表边种类", description = "侧拉框点边关系列表，过滤可以展示的边种类", parameters = {
            @Parameter(name = "str", description = "查询点VID", required = true),
            @Parameter(name = "type", description = "查询点类型", required = true)
    })
    public ApiResponse<List<String>> getVisbleSideRelation(@RequestParam("str") String str,
                                             @RequestParam("type") String type){
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(type)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_LEAK);
        }

        return atlasService.getVisibleSideRelation(str, type);
    }

    @PostMapping("/focusTag")
    @Operation(summary = "多节点关联关系查询", description = "查询多节点之间的关联关系")
    public ApiResponse<List<FocusTagVo>> getFocusTagRelation(@RequestBody List<FocusTagCondition> condition) {

        if (CollectionUtils.isEmpty(condition) || condition.size() < 2) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        return atlasService.getFocusTagRelation(condition);
    }

    @GetMapping("/history/query")
    @Operation(summary = "图探索探索历史检索", description = "查询探索历史记录", parameters = {
            @Parameter(name = "atlas_type", description = "检索类型（1-目标ID检索；2-关键词检索；3-子图遍历；）", required = true),
            @Parameter(name = "current_page", description = "当前页码", required = true),
            @Parameter(name = "page_size", description = "每页数量", required = true),
            @Parameter(name = "sort_order", description = "排序方式(asc/desc)", required = true)
    })
    public ApiResponse<AtlasHistoryVo> queryAtlasHistory(@RequestParam(name = "atlas_type") Integer atlasType,
                                         @RequestParam(name = "current_page") Integer currentPage,
                                         @RequestParam(name = "page_size") Integer pageSize,
                                         @RequestParam(name = "sort_order") String sortOrder) {
        try {
            // 构建查询参数
            JSONObject params = new JSONObject();
            params.put("atlas_type", atlasType);
            params.put("current_page", currentPage);
            params.put("page_size", pageSize);
            params.put("sort_order", sortOrder);
            return atlasService.queryAtlasHistory(params);
        } catch (Exception e) {
            log.error("查询探索历史失败！error->", e);
            return ApiResponse.error("查询探索历史失败!");
        }
    }

    @DeleteMapping("/history/delete")
    @Operation(summary = "图探索探索历史删除", description = "删除探索历史记录", parameters = {
            @Parameter(name = "id_list", description = "目标ID列表", required = true),
            @Parameter(name = "delete_all", description = "是否删除所有", required = true)
    })
    public ApiResponse<String> deleteAtlasHistory(@RequestParam(name = "id_list") String idList,@RequestParam(name = "delete_all") Boolean deleteAll){
        try {
            JSONObject params = new JSONObject();
            params.put("id_list", idList);
            params.put("delete_all", deleteAll);
            return atlasService.deleteAtlasHistory(params);
        } catch (Exception e) {
            log.error("删除探索历史失败！error->", e);
            return ApiResponse.error("删除探索历史失败!");
        }
    }

    @GetMapping("/ip/domain/edge")
    @Operation(summary = "IP域名证书详情关系图（会话分析）", description = "获取IP、域名、证书详情关系图（会话分析）", parameters = {
            @Parameter(name = "str", description = "查询字符串", required = true),
            @Parameter(name = "type", description = "查询类型", required = true)
    })
    public ApiResponse<VertexAssociationVo> getDemo(@RequestParam("str") String str, @RequestParam("type") String type) {
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(type)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        VertexAssociationVo vertexAssociationVo = vertexService.getAssociation(str, type);
        return ApiResponse.success(vertexAssociationVo);
    }

    @PostMapping("/ip/domain/edge/next")
    @Operation(summary = "IP域名证书详情关系图（图探索）", description = "获取IP、域名、证书详情关系图（图探索）")
    public ApiResponse<VertexAssociationNextVo> getDemoNext(@RequestBody GraphNextInfoCondition condition) {
        String str = condition.getStr();
        String type = condition.getType();
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(type)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        List<GraphNextInfoCondition.EdgeNum> edgeInfo = condition.getEdgeInfo();
        for (GraphNextInfoCondition.EdgeNum edgeNum : edgeInfo) {
            if (StringUtils.isEmpty(edgeNum.getEdge()) || edgeNum.getNum() < 1) {
                throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
            }
        }
        VertexAssociationNextVo vertexAssociationNextVo = vertexService.getAssociationNext(condition);
        return ApiResponse.success(vertexAssociationNextVo);
    }

    @GetMapping("/edge/properties/next")
    @Operation(summary = "边属性下一层查询", description = "根据边属性查询下一层数据", parameters = {
            @Parameter(name = "tag_name", description = "点名称", required = true),
            @Parameter(name = "properties_name", description = "属性名称", required = true),
            @Parameter(name = "properties_value", description = "属性值", required = true)
    })
    public ApiResponse<VertexAssociationNextVo> getEdgePropertiesNext(@RequestParam(name = "tag_name") String tagName,
                                             @RequestParam(name = "properties_name") String propertiesName,
                                             @RequestParam(name = "properties_value") String propertiesValue) {
        GraphPropertiesNextCondition condition = new GraphPropertiesNextCondition();
        condition.setTagName(tagName);
        condition.setPropertiesName(propertiesName);
        condition.setPropertiesValue(propertiesValue);
        if (StringUtils.isEmpty(condition.getTagName()) || StringUtils.isEmpty(condition.getPropertiesName()) || StringUtils.isEmpty(condition.getPropertiesValue())) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        VertexAssociationNextVo vertexAssociationNextVo = vertexService.getEdgePropertiesNext(condition);
        return ApiResponse.success(vertexAssociationNextVo);
    }

    @GetMapping("/tag/edge/json")
    @Operation(summary = "获取图关联JSON文件", description = "读取parse_conf.json文件内容", parameters = {})
    public ApiResponse<List<TagEdgeVo>> getJson() throws IOException {
        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            // 读取parse_conf文件
            InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("dict/analysis/parse_conf.json");
            if (resourceAsStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(resourceAsStream, StandardCharsets.UTF_8));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }

            String jsonStr = stringBuilder.toString();
            return ApiResponse.success(JSONArray.parseArray(jsonStr, TagEdgeVo.class));
        } catch (Exception e) {
            log.error("读取parse_conf文件失败！error->", e);

        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ex) {
                    throw ex;
                }
            }
        }
        return ApiResponse.error("图关联JSON文件未初始化");
    }

    @GetMapping("/ip/info")
    @Operation(summary = "查询IP详情", description = "查询IP的详细信息", parameters = {
            @Parameter(name = "str", description = "IP地址", required = true)
    })
    public ApiResponse<IpInfoVo> getIpInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        try {
            return metadata2Service.getIpInfo(str);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("会话侧拉，IP详情，e={}", e);
            throw new BusinessException(ErrorCode.IP_DETAIL_QUERY_ERROR);
        }
    }

    //todo
    @GetMapping("/cert/info")
    @Operation(summary = "查询证书详情", description = "查询证书的详细信息", parameters = {
            @Parameter(name = "str", description = "证书ID", required = true)
    })
    public ApiResponse<CertDetailVo> getCertInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str))   {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        try{
            return metadata2Service.getCertDetail(str);
        }catch (Exception e){
            log.error("证书详情查询失败,e->",e);
            throw new BusinessException(ErrorCode.CERT_DETAIL_QUERY_ERROR);
        }
    }

    @GetMapping("/cert/log/template")
    @Operation(summary = "查询当前用户的证书元数据模板样式JSON", description = "查询当前用户的证书元数据模板样式JSON（key为键，value为键名称）", parameters = {
            @Parameter(name = "user_id", description = "用户ID", required = true)
    })
    public ApiResponse<Map<String, Object>> getCertLogTemplate(@RequestParam(value = "user_id") Integer userId) {
        return certLogTemplateService.getUserTemplate(userId);
    }

    @PutMapping("/cert/log/template")
    @Operation(summary = "修改当前用户的证书元数据模板样式JSON", description = "修改当前用户的证书元数据模板样式JSON")
    public ApiResponse<String> modifyCertLogTemplate(@RequestBody ModifyCertLogTemplateCondition condition) {
        Integer userId = condition.getUserId();
        String key = condition.getKey();
        String value = condition.getValue();
        if (userId == null || StringUtils.isEmpty(key) || StringUtils.isEmpty(value)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        return certLogTemplateService.modifyUserTemplate(userId, key, value);
    }

    @GetMapping("/domain/info")
    @Operation(summary = "查询域名详情", description = "查询域名的详细信息", parameters = {
            @Parameter(name = "str", description = "域名地址", required = true)
    })
    public ApiResponse<DomainInfoVo> getDomainInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        try {
            return metadata2Service.getDomainInfo(str);
        } catch (Exception e) {
            log.error("域名详情查询失败，e->", e);
            throw new BusinessException(ErrorCode.DOMAIN_DETAIL_QUERY_ERROR);
        }
    }

    @GetMapping("/org/info")
    @Operation(summary = "查询企业详情", description = "查询企业的详细信息", parameters = {
            @Parameter(name = "str", description = "企业ID", required = true)
    })
    public ApiResponse<OrgInfoVo> getOrgInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        return metadata2Service.getOrgInfo(str);
    }

    @GetMapping("/sslfinger/info")
    @Operation(summary = "查询指纹详情", description = "查询指纹的详细信息", parameters = {
            @Parameter(name = "str", description = "指纹ID", required = true)
    })
    public ApiResponse<SSLFingerInfoVo> getFingerInfo(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_EMPTY);
        }
        try {
            return metadata2Service.getSSLFingerInfo(str);
        } catch (Exception e) {
            log.error("指纹详情信息查询失败，e->", e);
            throw new BusinessException(ErrorCode.FINGER_DETAIL_QUERY_ERROR);
        }
    }

    @GetMapping("/appservice/info")
    @Operation(summary = "查询应用服务详情", description = "查询应用服务的详细信息", parameters = {
            @Parameter(name = "str", description = "应用服务ID", required = true)
    })
    public ApiResponse<AppServiceInfoVo> getAppService(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        try {
            return metadata2Service.getAppService(str);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("应用服务详情信息查询失败，e={}", e);
            throw new BusinessException(ErrorCode.APPSERVICE_DETAIL_QUERY_ERROR);
        }
    }

    @GetMapping("/app/info")
    @Operation(summary = "查询应用详情", description = "查询应用的详细信息", parameters = {
            @Parameter(name = "str", description = "应用ID", required = true)
    })
    public ApiResponse<AppInfoVo> getApp(@RequestParam("str") String str) {
        if (StringUtils.isEmpty(str)) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        try {
            return metadata2Service.getApp(str);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("应用详情信息查询失败，e={}", e);
            throw new BusinessException(ErrorCode.APP_DETAIL_QUERY_ERROR);
        }
    }

    @PutMapping("label")
    @Operation(summary = "详情修改标签", description = "详情修改标签信息")
    public ApiResponse<String> updateLabels(@RequestBody NbLabelUpCondition condition) {
        try {
            return metadata2Service.updateLabels(condition);
        } catch (Exception e) {
            log.error("修改会话标签失败，e->", e);
            throw new BusinessException(ErrorCode.UPDATE_LABELS_ERROR);
        }
    }

    @PutMapping("remark")
    @Operation(summary = "详情修改备注", description = "详情修改标签信息")
    public ApiResponse<String> updateRemark(@RequestBody NbRemarkUpCondition condition) {
        try {
            return metadata2Service.updateRemark(condition);
        } catch (Exception e) {
            log.error("修改实体备注信息失败，e->", e);
            throw new BusinessException(ErrorCode.UPDATE_REMARK_ERROR);
        }
    }
}